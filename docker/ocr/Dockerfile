FROM debian:bullseye-slim

ENV DEBIAN_FRONTEND=noninteractive

ARG LOAD_LANG=eng

RUN apt update \
  && apt install -y --no-install-recommends \
    --option=Dpkg::Options::="--force-confdef" \
    --option=Dpkg::Options::="--force-confold" \
    ca-certificates \
    libtesseract-dev=4.1.1-2.1 \
    tesseract-ocr=4.1.1-2.1 \
    golang=2:1.15~1 \
    git \
    build-essential

ENV GO111MODULE=on
ENV GOPATH=${HOME}/go
ENV PATH=${PATH}:${GOPATH}/bin

RUN git clone https://github.com/osman-emek/ocrserver.git $GOPATH/src/github.com/osman-emek/ocrserver
WORKDIR $GOPATH/src/github.com/osman-emek/ocrserver

RUN go mod download
RUN go install .

# Load languages
RUN if [ -n "${LOAD_LANG}" ]; then apt-get install -y tesseract-ocr-${LOAD_LANG}; fi

ENV PORT=8282
CMD ["ocrserver"]