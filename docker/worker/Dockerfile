FROM ubuntu:22.04

RUN echo 'APT::Install-Suggests "0";' >> /etc/apt/apt.conf.d/00-docker
RUN echo 'APT::Install-Recommends "0";' >> /etc/apt/apt.conf.d/00-docker

RUN ln -snf /usr/share/zoneinfo/Etc/UTC /etc/localtime && echo Etc/UTC > /etc/timezone

WORKDIR /code

RUN apt-get update && \
    apt-get install -y software-properties-common gnupg2 && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update

RUN apt-get update \
  && apt-get install -y --no-install-recommends \
  --option=Dpkg::Options::="--force-confdef" \
  --option=Dpkg::Options::="--force-confold" \
  git \
  python3 \
  python3-pip \
  python3-venv \
  tzdata \
  libopencv-dev \
  python3-opencv \
  build-essential \
  cmake \
  pkg-config \
  libx11-dev \
  libopenblas-dev \
  libgtk-3-dev \
  libboost-python-dev

RUN apt-get update
RUN apt-get install -y python3.11
RUN apt-get install -y python3.11-venv
RUN apt-get install -y python3.11-distutils

RUN mkdir -p /code/savedvideofiles

COPY ./requirements.txt /code/requirements.txt
COPY ./config.yaml /code/config.yaml
COPY ./app /code/app

#RUN python3 -m venv /venv

#ENV PATH="/venv/bin:$PATH"
#RUN pip3 install --upgrade pip

RUN python3.11 -m pip install --no-cache-dir -r /code/requirements.txt