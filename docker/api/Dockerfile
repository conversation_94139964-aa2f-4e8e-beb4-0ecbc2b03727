FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11

ENV DEBIAN_FRONTEND=noninteractive

WORKDIR /code

RUN apt-get update \
  && apt-get install -y --no-install-recommends \
  --option=Dpkg::Options::="--force-confdef" \
  --option=Dpkg::Options::="--force-confold" \
  python3-opencv \
  build-essential \
  cmake \
  pkg-config \
  libx11-dev \
  libopenblas-dev \
  libgtk-3-dev \
  libboost-python-dev

COPY ./requirements.txt /code/requirements.txt
COPY ./config.yaml /code/config.yaml
COPY ./app /code/app

#RUN apt-get update && apt-get install -y \
#  python3.11 \
#  python3.11-venv \
#  python3.11-dev

#RUN python -m venv /venv
#
#ENV PATH="/venv/bin:$PATH"

#RUN pip install --upgrade pip
RUN python3.11 -m pip install --no-cache-dir -r /code/requirements.txt