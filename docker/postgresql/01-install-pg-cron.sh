#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create the extension
    CREATE EXTENSION IF NOT EXISTS pg_cron;
    
    -- Grant permissions
    GRANT USAGE ON SCHEMA cron TO postgres;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cron TO postgres;
    GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA cron TO postgres;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cron TO postgres;

    -- Create cleanup function
    CREATE OR REPLACE FUNCTION cleanup_expired_jobs()
    R<PERSON><PERSON>NS void AS \$\$
    BEGIN
        DELETE FROM jobs WHERE status = 'COMPLETED' AND created_at < NOW() - INTERVAL '7 days';
        DELETE FROM jobs WHERE status IN ('FAILED', 'TIMEOUT') AND created_at < NOW() - INTERVAL '30 days';
    END;
    \$\$ LANGUAGE plpgsql;

    -- Schedule the cleanup job
    SELECT cron.schedule('cloudml_cleanup_expired_jobs', '10 0 * * *', 'SELECT cleanup_expired_jobs();');
EOSQL
