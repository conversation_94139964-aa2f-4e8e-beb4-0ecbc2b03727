# PostgreSQL pg_cron Automated Cleanup

This document describes the automated cleanup system implemented using PostgreSQL's pg_cron extension to periodically clean up expired job data.

## Overview

The system automatically cleans up expired job data from the database using a scheduled PostgreSQL function that runs daily at 00:10 UTC. This replaces the need for manual cleanup operations and ensures consistent database maintenance.

## Implementation Details

### Database Tables Affected

The cleanup process removes data from three related tables in the correct order to maintain referential integrity:

1. **job_results** - Deleted first (due to foreign key constraints)
2. **job_steps** - Deleted second 
3. **jobs** - Deleted last (parent table)

### Cleanup Criteria

Jobs are eligible for cleanup when:
- `expires_at` timestamp is in the past (compared to current UTC time)
- Job state is either `'completed'` or `'failed'`
- Only finished jobs are cleaned up to avoid removing active processing jobs

### Files Modified/Added

#### New Files
- `app/utils/database_utils.py` - Core database utilities for pg_cron management
- `tools/_test_pg_cron_cleanup.py` - Test script for validation

#### Modified Files  
- `app/services/model_preloader.py` - Added cleanup initialization to startup
- `app/routes/stat.py` - Added monitoring endpoints
- `config.yaml` - Added pg_cron configuration options

## Configuration

Add these settings to your `config.yaml`:

```yaml
job_tracking:
  retention_days: 30
  # pg_cron cleanup configuration  
  pg_cron_cleanup_enabled: true
  cleanup_schedule: "10 0 * * *"  # Daily at 00:10 UTC
```

### Configuration Options

- `pg_cron_cleanup_enabled`: Enable/disable the pg_cron cleanup system (default: true)
- `cleanup_schedule`: Cron schedule for cleanup job (default: "10 0 * * *" - daily at 00:10)
- `retention_days`: Number of days to retain job data (used for expires_at calculation)

## Database Functions

### cleanup_expired_jobs()

PostgreSQL function that performs the cleanup operation:

```sql
CREATE OR REPLACE FUNCTION cleanup_expired_jobs()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
    job_uuids TEXT[];
BEGIN
    -- Find expired job UUIDs for jobs that are completed or failed
    SELECT ARRAY(
        SELECT job_uuid 
        FROM jobs 
        WHERE expires_at < NOW() AT TIME ZONE 'UTC'
        AND state IN ('completed', 'failed')
    ) INTO job_uuids;
    
    -- Get count of jobs to be cleaned up
    expired_count := array_length(job_uuids, 1);
    
    IF expired_count > 0 THEN
        -- Delete in correct order for referential integrity
        DELETE FROM job_results WHERE job_uuid = ANY(job_uuids);
        DELETE FROM job_steps WHERE job_uuid = ANY(job_uuids);
        DELETE FROM jobs WHERE job_uuid = ANY(job_uuids);
        
        RAISE NOTICE 'Cleaned up % expired jobs at %', 
            expired_count, NOW() AT TIME ZONE 'UTC';
    END IF;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;
```

### pg_cron Job

The scheduled job is created with:

```sql
SELECT cron.schedule(
    'cloudml_cleanup_expired_jobs',
    '10 0 * * *',
    'SELECT cleanup_expired_jobs();'
);
```

## API Endpoints

### Monitoring Endpoints

#### GET /cleanup/status
Returns the current status of the pg_cron cleanup job and statistics.

**Response:**
```json
{
  "pg_cron_job": {
    "job_exists": true,
    "job_name": "cloudml_cleanup_expired_jobs", 
    "schedule": "10 0 * * *",
    "command": "SELECT cleanup_expired_jobs();",
    "active": true,
    "job_id": 1
  },
  "cleanup_statistics": {
    "total_expired": 150,
    "by_state": {
      "completed": {
        "count": 120,
        "oldest_expiry": "2024-01-15T10:30:00",
        "newest_expiry": "2024-01-20T15:45:00"
      },
      "failed": {
        "count": 30,
        "oldest_expiry": "2024-01-16T08:15:00", 
        "newest_expiry": "2024-01-20T12:30:00"
      }
    }
  },
  "timestamp": "2024-01-21T09:15:30"
}
```

#### POST /cleanup/manual
Manually triggers the cleanup function for testing purposes.

**Response:**
```json
{
  "manual_cleanup_result": {
    "success": true,
    "cleaned_jobs": 45,
    "timestamp": "2024-01-21T09:15:30"
  },
  "timestamp": "2024-01-21T09:15:30"
}
```

## Initialization Process

The pg_cron cleanup system is automatically initialized during application startup:

1. **Function Creation**: The `cleanup_expired_jobs()` function is created/updated
2. **Extension Setup**: pg_cron extension is enabled (if permissions allow)
3. **Job Scheduling**: The cleanup job is scheduled to run daily at 00:10 UTC
4. **Verification**: System verifies the job was created successfully
5. **Statistics**: Initial cleanup statistics are logged

## Testing

Use the provided test script to validate the implementation:

```bash
cd /path/to/cloudml-v3
python tools/_test_pg_cron_cleanup.py
```

The test script verifies:
- ✅ Cleanup function creation
- ✅ pg_cron job setup  
- ✅ Job status verification
- ✅ Manual cleanup execution
- ✅ Statistics retrieval
- ✅ Full system initialization

## Prerequisites

### PostgreSQL pg_cron Extension

The system requires the pg_cron extension to be available in PostgreSQL:

```sql
-- Enable the extension (requires superuser privileges)
CREATE EXTENSION IF NOT EXISTS pg_cron;
```

### Database Permissions

The application database user needs:
- CREATE function privileges
- INSERT/DELETE privileges on cron.job table (for pg_cron)
- EXECUTE privileges on the cleanup function

## Monitoring and Maintenance

### Logging

The cleanup operation logs information through:
- PostgreSQL NOTICE messages (visible in logs)
- Application startup logs for initialization
- API endpoint logs for manual operations

### Health Checks

Monitor the system through:
- `/cleanup/status` endpoint for current status
- PostgreSQL logs for NOTICE messages from cleanup operations
- Application startup logs for initialization status

## Troubleshooting

### Common Issues and Solutions

#### 1. pg_cron Extension Not Available

**Error Message:**
```
extension "pg_cron" is not available
DETAIL: Could not open extension control file "/usr/share/postgresql/15/extension/pg_cron.control": No such file or directory.
```

**Solution Options:**

**A. Install pg_cron Extension (Recommended)**

1. **For Ubuntu/Debian:**
   ```bash
   sudo apt-get install postgresql-15-cron
   ```

2. **For CentOS/RHEL:**
   ```bash
   sudo yum install pg_cron_15
   ```

3. **For Docker PostgreSQL:**
   ```dockerfile
   FROM postgres:15
   RUN apt-get update && apt-get install -y postgresql-15-cron
   ```

4. **Configure PostgreSQL:**
   ```sql
   -- Add to postgresql.conf
   shared_preload_libraries = 'pg_cron'
   cron.database_name = 'your_database_name'
   ```

5. **Restart PostgreSQL and enable extension:**
   ```sql
   CREATE EXTENSION pg_cron;
   ```

**B. Use Alternative Scheduling (Fallback)**

If you cannot install pg_cron, the system automatically provides fallback options:

1. **System Cron Job (Recommended Alternative):**
   ```bash
   # Add to crontab (crontab -e)
   10 0 * * * /usr/bin/python3 /path/to/cloudml-v3/tools/_cleanup_cron_helper.py
   
   # Or using curl directly
   10 0 * * * curl -X POST http://localhost:8080/cleanup/manual -H "Authorization: Bearer YOUR_TOKEN"
   ```

2. **Manual Cleanup via API:**
   ```bash
   curl -X POST http://localhost:8080/cleanup/manual
   ```

3. **Application-Level Scheduler:**
   - Use APScheduler within the application
   - Implement custom background task scheduler

#### 2. Permission Denied Errors

**Error:** Database user lacks privileges for pg_cron operations.

**Solution:**
```sql
-- Grant necessary permissions
GRANT USAGE ON SCHEMA cron TO your_app_user;
GRANT INSERT, UPDATE, DELETE ON cron.job TO your_app_user;
GRANT EXECUTE ON FUNCTION cleanup_expired_jobs() TO your_app_user;
```

#### 3. Transaction Rollback Issues

**Error:** `current transaction is aborted, commands ignored until end of transaction block`

**Solution:** The updated implementation handles this by:
- Checking pg_cron availability before attempting to use it
- Using separate transactions for extension setup and job creation
- Graceful fallback when pg_cron is not available

### Checking System Status

Use the status endpoint to verify your setup:

```bash
curl http://localhost:8080/cleanup/status
```

**Example Response:**
```json
{
  "cleanup_system": {
    "cleanup_function_exists": true,
    "pg_cron_available": false,
    "pg_cron_job_status": null,
    "cleanup_statistics": {
      "total_expired": 45,
      "by_state": {
        "completed": {"count": 30},
        "failed": {"count": 15}
      }
    },
    "available_modes": [
      {
        "mode": "pg_cron",
        "name": "PostgreSQL pg_cron",
        "available": false,
        "automated": true
      },
      {
        "mode": "manual_api",
        "name": "Manual API Cleanup",
        "available": true,
        "automated": false
      },
      {
        "mode": "system_cron",
        "name": "System Cron Job",
        "available": true,
        "automated": true
      }
    ],
    "recommendations": {
      "primary": "system_cron",
      "message": "pg_cron not available. Recommended: Use system cron job to call manual cleanup endpoint",
      "setup_required": true,
      "installation_guide": {
        "pg_cron_install": "To install pg_cron: 1) Install extension package 2) Add 'shared_preload_libraries = pg_cron' to postgresql.conf 3) Restart PostgreSQL 4) CREATE EXTENSION pg_cron;",
        "system_cron_example": "Add to crontab: 10 0 * * * curl -X POST http://localhost:8080/cleanup/manual -H 'Authorization: Bearer YOUR_TOKEN'"
      }
    }
  }
}
```

## Fallback System

The cleanup system is designed to work even when pg_cron is not available. The application automatically detects the availability of pg_cron and provides appropriate fallback mechanisms.

### Automatic Fallback Behavior

1. **During Startup:**
   - System checks if pg_cron extension is available
   - If available: Sets up the automated pg_cron job
   - If not available: Creates cleanup function and logs alternative options
   - Application continues to start successfully regardless of pg_cron availability

2. **Configuration:**
   ```yaml
   job_tracking:
     retention_days: 30
     pg_cron_cleanup_enabled: true
     cleanup_schedule: "10 0 * * *"
     # Fallback behavior when pg_cron is not available
     fallback_on_pg_cron_failure: true
     alternative_cleanup:
       manual_api_enabled: true
       system_cron_helper: "tools/_cleanup_cron_helper.py"
       log_alternative_recommendations: true
   ```

### Alternative Cleanup Methods

#### 1. System Cron Job

**Setup using the provided helper script:**
```bash
# Make the script executable
chmod +x /path/to/cloudml-v3/tools/_cleanup_cron_helper.py

# Add to crontab
crontab -e
# Add this line:
10 0 * * * /usr/bin/python3 /path/to/cloudml-v3/tools/_cleanup_cron_helper.py
```

**Environment Variables for the helper script:**
```bash
export CLOUDML_API_URL="http://localhost:8080"
export CLOUDML_AUTH_TOKEN="your_bearer_token"  # If auth enabled
export CLEANUP_LOG_FILE="/var/log/cloudml_cleanup.log"
export CLEANUP_TIMEOUT="30"
```

#### 2. Direct API Calls

**Using curl:**
```bash
# Manual cleanup
curl -X POST http://localhost:8080/cleanup/manual

# With authentication
curl -X POST http://localhost:8080/cleanup/manual \
     -H "Authorization: Bearer YOUR_TOKEN"

# Check status
curl http://localhost:8080/cleanup/status
```

#### 3. Application-Level Scheduling

If you prefer to handle scheduling within the application, you can use Python's APScheduler:

```python
from apscheduler.schedulers.background import BackgroundScheduler
from app.utils.database_utils import database_utils

def schedule_cleanup():
    scheduler = BackgroundScheduler()
    scheduler.add_job(
        func=database_utils.manual_cleanup_test,
        trigger="cron",
        hour=0,
        minute=10,
        id='cleanup_job'
    )
    scheduler.start()
```

## Performance Considerations

- The cleanup uses bulk DELETE operations for efficiency
- Foreign key constraints are respected through proper deletion order
- Indexes on expires_at and state columns optimize query performance
- NOTICE logging provides visibility without excessive overhead

## Security

- Function uses parameterized queries to prevent SQL injection
- Only targets specific job states to avoid accidental deletion
- UTC timestamps prevent timezone-related issues
- Bulk operations minimize database lock time

## Comparison with Python Implementation

The SQL implementation provides equivalent functionality to the Python `cleanup_expired_jobs()` method:

| Aspect | Python Method | SQL Function |
|--------|---------------|--------------|
| **Scheduling** | Manual/cron outside app | Automatic via pg_cron |
| **Performance** | Good | Excellent (database-native) |
| **Reliability** | Depends on app uptime | Independent of application |
| **Monitoring** | Application logs | Database logs + API |
| **Maintenance** | Requires app deployment | Database-level management |

The pg_cron implementation provides better reliability and performance while maintaining the same cleanup logic and safety measures.