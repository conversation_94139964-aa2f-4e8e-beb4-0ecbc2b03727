# Job Restart Diagnostic Guide

## 🔍 **Potential Root Causes Analysis**

Based on the code analysis, here are the possible reasons why jobs are still restarting:

### **1. Worker Process Restart/Crash**
**Issue**: Docker containers or worker processes might be restarting
**Check**:
```bash
# Monitor worker containers
docker logs worker-1 -f
docker logs worker-2 -f

# Check if containers are restarting
docker ps -a
docker stats worker-1 worker-2
```

### **2. RabbitMQ Message Redelivery**
**Issue**: Messages being redelivered by RabbitMQ broker
**Check**:
```bash
# Check RabbitMQ management interface
curl -u rabbitusr:1lqgEJU3VPyhg http://localhost:15672/api/queues

# Monitor message acknowledgments
docker logs rabbitmq -f | grep "video_processing"
```

### **3. Database Transaction Issues**
**Issue**: Job state updates not committed properly
**Check**:
```sql
-- Check job states in PostgreSQL
SELECT uuid, state, step, created_at, updated_at, task_id 
FROM jobs 
ORDER BY created_at DESC 
LIMIT 10;

-- Look for duplicate UUIDs
SELECT uuid, COUNT(*) 
FROM jobs 
GROUP BY uuid 
HAVING COUNT(*) > 1;
```

### **4. Step Function Exceptions**
**Issue**: Individual step functions still raising exceptions
**Solution**: Already fixed in celery_app.py

### **5. Memory/Resource Issues**
**Issue**: Workers running out of memory and restarting
**Check**:
```bash
# Monitor resource usage
docker stats
free -h
nvidia-smi  # if using GPU
```

### **6. Application-Level Job Duplication**
**Issue**: Multiple API calls creating same UUID
**Check**: Add UUID logging to track job creation

## 🛠️ **Debugging Steps**

### **Step 1: Add Enhanced Logging**
Add this to the beginning of `process_video` function:

```python
@celery_app.task(bind=True, autoretry_for=(), max_retries=0, retry_backoff=False)
def process_video(self, job_uuid: str, task_params_dict: dict) -> dict:
    logger.info(f"🚀 STARTING process_video for UUID: {job_uuid}, Task ID: {self.request.id}")
    logger.info(f"📊 Task retry count: {self.request.retries}")
    logger.info(f"🔄 Task origin: {self.request.origin}")
    
    # ... rest of function
```

### **Step 2: Monitor Job States**
Add job state logging:

```python
def log_job_state_change(job_uuid: str, old_state: str, new_state: str):
    logger.info(f"🔄 Job {job_uuid}: {old_state} → {new_state}")
```

### **Step 3: Check for External Restart Triggers**

1. **Container Restart Policy**
   ```bash
   docker inspect worker-1 | grep -A5 "RestartPolicy"
   ```

2. **Health Check Failures**
   ```bash
   docker inspect worker-1 | grep -A10 "Health"
   ```

3. **Resource Limits**
   ```bash
   docker inspect worker-1 | grep -A10 "Resources"
   ```

### **Step 4: Validate UUID Uniqueness**
Check if the same UUID is being sent multiple times:

```python
# In main.py or detection service
@app.middleware("http")
async def log_requests(request: Request, call_next):
    if request.url.path == "/object_detection":
        body = await request.body()
        data = json.loads(body)
        uuid = data.get("file", {}).get("uuid")
        logger.info(f"📨 Received request for UUID: {uuid}")
    response = await call_next(request)
    return response
```

## 🚨 **Immediate Actions to Take**

### **1. Update Celery Configuration (CRITICAL)**
The current configuration has been fixed, but verify these settings:

```python
# In celery_app.py - these should be set:
task_acks_late=False,  # ✅ Fixed
task_reject_on_worker_lost=False,  # ✅ Fixed  
autoretry_for=(),  # ✅ Fixed
max_retries=0,  # ✅ Fixed
```

### **2. Monitor Worker Processes**
```bash
# Run this to monitor workers
watch -n 1 'docker ps | grep worker'
```

### **3. Check RabbitMQ Queue Status**
```bash
# Monitor message flow
curl -u rabbitusr:1lqgEJU3VPyhg \
     http://localhost:15672/api/queues/%2F/video_processing
```

### **4. Database Monitoring**
```sql
-- Real-time job monitoring
SELECT 
    uuid, 
    state, 
    step, 
    task_id,
    created_at,
    updated_at,
    EXTRACT(EPOCH FROM (updated_at - created_at)) as duration_seconds
FROM jobs 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;
```

## 🔧 **Final Solution Verification**

After implementing the fixes, verify with these commands:

1. **Send a test job**:
   ```bash
   curl -X POST http://localhost:8000/object_detection \
        -H "Content-Type: application/json" \
        -d '{
          "file": {"uuid": "test-'$(date +%s)'"},
          "resources": {"method": "POST", "url": "http://example.com/test.mp4"},
          "db": {"configuration": {"person_detection": {"enabled": true}}},
          "response": {"callback": "http://example.com/callback"}
        }'
   ```

2. **Monitor the job**:
   ```bash
   # Watch logs
   docker logs worker-1 -f | grep "test-"
   
   # Check database
   psql -h localhost -U postgres -d object_detection \
        -c "SELECT * FROM jobs WHERE uuid LIKE 'test-%';"
   ```

3. **Verify single execution**:
   - Job should appear only once in database
   - Only one set of processing logs
   - No duplicate callback calls

## 📋 **Expected Behavior After Fix**

✅ **Job Flow Should Be**:
1. Receive request → Create job (state: "queued")
2. Worker picks up → Update to "started"  
3. Process steps → Various step states
4. Complete successfully → Update to "completed"
5. **NO RESTART** - Job stays completed

❌ **Warning Signs**:
- Same UUID appearing multiple times in logs
- Job state changing from "completed" back to "started"
- Multiple task IDs for same UUID
- Container restart logs
- Memory/resource exhaustion errors

Use this diagnostic guide to identify the specific cause of job restarts in your environment.