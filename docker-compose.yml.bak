services:
  # RabbitMQ message broker
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    networks:
      - deployml_network
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: rabbitusr
      RABBITMQ_DEFAULT_PASS: 1lqgEJU3VPyhg
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL for job tracking
  postgresql:
    build:
      context: .
      dockerfile: docker/postgresql/Dockerfile
    container_name: postgresql
    networks:
      - deployml_network
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "4GfW42eVb"
      POSTGRES_DB: "object_detection"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
    command: 
      - "postgres"
      - "-c"
      - "shared_preload_libraries=pg_cron"
      - "-c"
      - "cron.database_name=object_detection"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Main API service
  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
    container_name: api
    networks:
      - deployml_network
    ports:
      - "8080:8080"
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    command: gunicorn -k uvicorn.workers.UvicornWorker app.main:app -w 8 -b 0.0.0.0:8080

  # Celery workers
  worker-1:
    build:
      context: .
      dockerfile: docker/worker/Dockerfile
    container_name: worker-1
    networks:
      - deployml_network
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    command: celery -A app.celery_app worker --loglevel=info --queues=video_processing -n worker@%h -P solo
    working_dir: /code

  ocr-server:
    container_name: ocrserver
    restart: unless-stopped
    networks:
      - deployml_network
    build:
      context: .
      dockerfile: docker/ocr/Dockerfile
    ports:
      - "8282:8282"
    environment:
      PORT: 8282

  open-alpr-server:
    container_name: openalprserver
    restart: unless-stopped
    networks:
      - deployml_network
    build:
      context: .
      dockerfile: docker/oalpr/Dockerfile
    ports:
      - "8181:8181"
    environment:
      PORT: 8181

volumes:
  rabbitmq_data:
  postgresql_data:
networks:
  deployml_network:
    driver: bridge