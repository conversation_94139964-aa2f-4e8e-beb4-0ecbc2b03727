#!/usr/bin/env python3
"""
System Cron Helper for Database Cleanup

This script can be used as an alternative to pg_cron for scheduling database cleanup.
It calls the manual cleanup API endpoint and logs the results.

Usage:
1. Add to system crontab:
   10 0 * * * /path/to/python /path/to/_cleanup_cron_helper.py

2. Or use with curl directly:
   10 0 * * * curl -X POST http://localhost:8080/cleanup/manual

Configuration:
- Set environment variables or modify the script for your environment
- API_URL: The base URL of your application API
- AUTH_TOKEN: Bearer token if authentication is enabled
"""

import os
import sys
import logging
import requests
from datetime import datetime
from typing import Optional

# Configuration - modify these for your environment
API_URL = os.getenv('CLOUDML_API_URL', 'http://localhost:8080')
AUTH_TOKEN = os.getenv('CLOUDML_AUTH_TOKEN', None)
LOG_FILE = os.getenv('CLEANUP_LOG_FILE', '/var/log/cloudml_cleanup.log')
TIMEOUT_SECONDS = int(os.getenv('CLEANUP_TIMEOUT', '30'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE) if LOG_FILE else logging.StreamHandler(),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def call_cleanup_api() -> bool:
    """
    Call the manual cleanup API endpoint.
    
    Returns:
        bool: True if cleanup successful, False otherwise
    """
    url = f"{API_URL}/cleanup/manual"
    headers = {'Content-Type': 'application/json'}
    
    if AUTH_TOKEN:
        headers['Authorization'] = f'Bearer {AUTH_TOKEN}'
    
    try:
        logger.info(f"Calling cleanup API: {url}")
        
        response = requests.post(
            url,
            headers=headers,
            timeout=TIMEOUT_SECONDS
        )
        
        if response.status_code == 200:
            result = response.json()
            cleanup_result = result.get('manual_cleanup_result', {})
            
            if cleanup_result.get('success'):
                cleaned_jobs = cleanup_result.get('cleaned_jobs', 0)
                logger.info(f"Cleanup successful: {cleaned_jobs} jobs cleaned")
                return True
            else:
                error = cleanup_result.get('error', 'Unknown error')
                logger.error(f"Cleanup failed: {error}")
                return False
        else:
            logger.error(f"API call failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False


def get_cleanup_status() -> Optional[dict]:
    """
    Get cleanup system status for logging.
    
    Returns:
        dict: Cleanup status or None if failed
    """
    url = f"{API_URL}/cleanup/status"
    headers = {'Content-Type': 'application/json'}
    
    if AUTH_TOKEN:
        headers['Authorization'] = f'Bearer {AUTH_TOKEN}'
    
    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=TIMEOUT_SECONDS
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"Status check failed with status {response.status_code}")
            return None
            
    except Exception as e:
        logger.warning(f"Status check failed: {str(e)}")
        return None


def main():
    """
    Main function to run cleanup and log results.
    """
    logger.info("Starting scheduled database cleanup")
    
    # Get initial status
    status = get_cleanup_status()
    if status:
        cleanup_system = status.get('cleanup_system', {})
        stats = cleanup_system.get('cleanup_statistics', {})
        total_expired = stats.get('total_expired', 0)
        logger.info(f"Found {total_expired} expired jobs ready for cleanup")
    
    # Perform cleanup
    success = call_cleanup_api()
    
    if success:
        logger.info("Database cleanup completed successfully")
        return 0
    else:
        logger.error("Database cleanup failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())