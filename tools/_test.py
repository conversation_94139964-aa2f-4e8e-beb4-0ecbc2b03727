import requests
import json
import uuid
import random

def getfileName(rnd):
    return "cargo"+str(rnd)+".mp4"

def cloudmltest(mcount=4, url=""):
    for i in range(mcount):
        # time.sleep(0.5)
        uuidval = str(uuid.uuid4())

        animalt = random.choice([True, False])
        vehiclet = random.choice([True, False])
        persont = random.choice([True, False])
        packaget = random.choice([True, False])
        licencept = random.choice([True, False])
        barcodet = random.choice([True, False])
        facerect = random.choice([True, False])

        hotzonet = random.choice([True, False])

        videos = ["barcode1", "cats", "composition1", "composition2", "composition3", "composition4", "dogs1", "dogs2",
                  "face_recognition_video1", "licence_plate1", "licence_plate2", "licence_plate3", "licence_plate4",
                  "mark", "melih", "package1", "package2", "package3", "package4", "person1", "person2", "person3",
                  "vehicle1", "vehicle2","notfound"]

        selected_video = random.choice(videos)
        selected_video = selected_video + ".mp4"

        r1 = random.randint(1, 5)
        fname = getfileName(r1)
        ppayload = {
          "resources": {
            "method": "GET",
            "url": "https://ezlo.uzay.space/static/video/"+selected_video
          },
          "file": {
            "uuid": uuidval,
            "timestamp": 3252353232,
            "size": 354235235,
            "filename": fname,
            "file_format": "mp4"
          },
          "db": {
            "elements": {
                "known_people": [
                    {
                        "uuid": "541d2a78-d42f-43f0-8df5-d8906b81ec66",
                        "name": "Osman",
                        "meta": {},
                        "files": [{
                            "uuid": "34c86861-815f-4f32-ab3b-7611595503bb",
                            "meta": {
                                "url": "https://ezlo.uzay.space/static/video/face_recognition_video1.jpg"
                            }
                        }]
                    },
                    {
                        "uuid": "541d2a78-d42f-43f0-8df5-d8906b81ec55",
                        "name": "John",
                        "meta": {},
                        "files": [{
                            "uuid": "c8839580-6c70-4e04-b7c9-2df0c5e7810a",
                            "meta": {
                                "url": "https://ezlo.uzay.space/static/video/face_recognition_video2.png"
                            }
                        }]
                    },
                    {
                        "uuid": "1a1585c0-3e10-415e-b8bf-3b31c85e5b3b",
                        "name": "Tom",
                        "meta": {},
                        "files": [{
                            "uuid": "5287165c-9c84-4458-9557-8c0fa86685cd",
                            "meta": {
                                "url": "https://ezlo.uzay.space/static/video/face_recognition_video3.png"
                            }
                        }]
                    }
                ]
            },
            "configuration": {
              "person_detection": {
                "enabled": persont
              },
              "package_detection": {
                "enabled": packaget
              },
              "animal_detection": {
                "enabled": animalt
              },
              "face_recognition": {
                "enabled": facerect
              },
              "barcode_detection": {
                "enabled": barcodet
              },
              "vehicle_detection": {
                "enabled": vehiclet
              },
              "plate_detection": {
                "enabled": licencept
              },
              "hotzones": {
                "enabled": hotzonet,
                "zones": [
                  {
                    "id": 1,
                    "maxwidth": 1920,
                    "maxheight": 1080,
                    "points": [
                        {},
                                            {
                                                "x": 0,
                                                "y": 0
                                            },
                                            {
                                                "x": 0,
                                                "y": 600
                                            },
                                            {
                                                "x": 1920,
                                                "y": 600
                                            },
                                            {
                                                "x": 1920,
                                                "y": 0
                                            }
                    ]
                  },
                  {
                    "id": 2,
                    "maxwidth": 1920,
                    "maxheight": 1080,
                    "points": [
                        {},
                                            {
                                                "x": 625,
                                                "y": 666
                                            },
                                            {
                                                "x": 729,
                                                "y": 623
                                            },
                                            {
                                                "y": 615,
                                                "x": 1102
                                            },
                                            {
                                                "y": 693,
                                                "x": 1291
                                            },
                                            {
                                                "y": 677,
                                                "x": 1484
                                            },
                                            {
                                                "x": 1484,
                                                "y": 872
                                            },
                                            {
                                                "x": 1314,
                                                "y": 1149
                                            },
                                            {
                                                "y": 1067,
                                                "x": 1078
                                            },
                                            {
                                                "x": 671,
                                                "y": 1067
                                            },
                                            {
                                                "y": 1067,
                                                "x": 264
                                            },
                                            {
                                                "y": 872,
                                                "x": 264
                                            },
                                            {
                                                "y": 677,
                                                "x": 264
                                            }
                    ]
                  }
                ]
              }
            },
            "files": {}
          },
          "response": {
            "callback": "https://webhook.site/73142aba-d4eb-4c34-86f6-63bfc2f10147/webhook"
          }
        }
        # print(ppayload)
        payload = json.dumps(ppayload)
        headers = {
          'Content-Type': 'application/json',
          # 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbiI6eyJ1dWlkIjoiNDQ4NmZiYzAtZjQ1MS0xMWVmLTlhYTctZWJkNjFiNGJiMGQxIiwiZXhwaXJlc190cyI6MTc0NDI5MTU3NSwiZ2VuZXJhdGVkX3RzIjoxNzQwNTgxNTA3LCJ0eXBlIjoiY29udHJvbGxlciJ9LCJsb2NhdGlvbiI6eyJpZCI6Mzc0NSwidXVpZCI6IjEzNjZlM2QwLWE3ZGEtMTFlOS1hYjUzLTA1OTY0YzBmZjFlNCJ9LCJvZW0iOnsiaWQiOjF9LCJjb250cm9sbGVyIjp7ImlkIjo5MDAyNzc2NiwidXVpZCI6IjJjOGU5MDMwLTMwNzktMTFlZC1hNGNkLTBkMWUwNjc0N2EyZiJ9LCJpYXQiOjE3NDA1ODE1MzUsImV4cCI6MTc0NTcwODk3NX0.diFBNciJXP9VEBhNsa-1m_rO-oMKLLLl5HfVQu1rv2k'
        }

        response = requests.request("POST", url, headers=headers, data=payload, verify=True)

        print(i, response.text)


#cloudmltest(5,"http://127.0.0.1:8080/object_detection")
cloudmltest(80,"https://dev-cloudmlv2.ezlo.com/object_detection")

