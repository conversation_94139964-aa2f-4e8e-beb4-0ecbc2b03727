import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.celery_app import celery_app

def revoke_all(terminate=False):
    i = celery_app.control.inspect()

    reserved = i.reserved() or {}
    scheduled = i.scheduled() or {}

    count = 0

    for worker, tasks in reserved.items():
        for task in tasks:
            celery_app.control.revoke(task['id'], terminate=terminate)
            count += 1

    for worker, tasks in scheduled.items():
        for task in tasks:
            celery_app.control.revoke(task['id'], terminate=terminate)
            count += 1

    print(f"{count} task(s) revoked.")

if __name__ == "__main__":
    revoke_all(terminate=True)
