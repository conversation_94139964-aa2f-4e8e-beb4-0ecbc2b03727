#!/usr/bin/env python3
"""
Test script for pg_cron cleanup functionality.

This script tests the pg_cron cleanup implementation by:
1. Checking if the cleanup function exists
2. Testing manual cleanup execution
3. Verifying pg_cron job status
4. Displaying cleanup statistics
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.database_utils import database_utils
from app.config.logger_factory import get_logger

logger = get_logger(__name__)


def test_cleanup_function():
    """Test if the cleanup function can be created successfully."""
    print("🔧 Testing cleanup function creation...")
    success = database_utils.create_cleanup_function()
    if success:
        print("✅ Cleanup function created successfully")
        return True
    else:
        print("❌ Failed to create cleanup function")
        return False


def test_pg_cron_setup():
    """Test pg_cron job setup."""
    print("\n🕐 Testing pg_cron job setup...")
    success = database_utils.setup_pg_cron_job()
    if success:
        print("✅ pg_cron job setup successfully")
        return True
    else:
        print("❌ Failed to setup pg_cron job")
        return False


def test_manual_cleanup():
    """Test manual cleanup execution."""
    print("\n🧹 Testing manual cleanup execution...")
    result = database_utils.manual_cleanup_test()
    if result.get("success"):
        cleaned_count = result.get("cleaned_jobs", 0)
        print(f"✅ Manual cleanup executed successfully - {cleaned_count} jobs cleaned")
        return True
    else:
        error = result.get("error", "Unknown error")
        print(f"❌ Manual cleanup failed: {error}")
        return False


def check_pg_cron_status():
    """Check the status of the pg_cron job."""
    print("\n📊 Checking pg_cron job status...")
    status = database_utils.check_pg_cron_status()
    
    if status.get("error"):
        print(f"❌ Error checking status: {status['error']}")
        return False
    
    if status.get("job_exists"):
        print("✅ pg_cron job exists:")
        print(f"   Job Name: {status.get('job_name')}")
        print(f"   Schedule: {status.get('schedule')}")
        print(f"   Command: {status.get('command')}")
        print(f"   Active: {status.get('active')}")
        print(f"   Job ID: {status.get('job_id')}")
        return True
    else:
        print("❌ pg_cron job does not exist")
        return False


def get_cleanup_statistics():
    """Display cleanup statistics."""
    print("\n📈 Getting cleanup statistics...")
    stats = database_utils.get_cleanup_statistics()
    
    if stats.get("error"):
        print(f"❌ Error getting statistics: {stats['error']}")
        return False
    
    total_expired = stats.get("total_expired", 0)
    print(f"📊 Total expired jobs ready for cleanup: {total_expired}")
    
    by_state = stats.get("by_state", {})
    if by_state:
        print("   Breakdown by state:")
        for state, state_stats in by_state.items():
            count = state_stats.get("count", 0)
            oldest = state_stats.get("oldest_expiry", "N/A")
            newest = state_stats.get("newest_expiry", "N/A")
            print(f"     {state}: {count} jobs (oldest: {oldest}, newest: {newest})")
    else:
        print("   No expired jobs found")
    
    return True


def test_full_initialization():
    """Test the complete initialization process."""
    print("\n🚀 Testing full pg_cron cleanup initialization...")
    success = database_utils.initialize_pg_cron_cleanup()
    if success:
        print("✅ Full initialization completed successfully")
        
        # Check what mode we're in
        status = database_utils.get_cleanup_mode_status()
        pg_cron_available = status.get("pg_cron_available", False)
        
        if pg_cron_available:
            print("   🎯 pg_cron mode: Automated cleanup scheduled")
        else:
            print("   🔄 Fallback mode: Manual cleanup available")
            recommendations = status.get("recommendations", {})
            if recommendations:
                print(f"   💡 Recommendation: {recommendations.get('message', 'Use alternative cleanup methods')}")
        
        return True
    else:
        print("❌ Full initialization failed")
        return False


def test_cleanup_mode_status():
    """Test the cleanup mode status functionality."""
    print("\n🔍 Testing cleanup mode status...")
    status = database_utils.get_cleanup_mode_status()
    
    if status.get("error"):
        print(f"❌ Error getting status: {status['error']}")
        return False
    
    print("✅ Cleanup mode status retrieved successfully:")
    print(f"   📝 Cleanup function exists: {status.get('cleanup_function_exists', False)}")
    print(f"   🔧 pg_cron available: {status.get('pg_cron_available', False)}")
    
    available_modes = status.get("available_modes", [])
    print(f"   🛠️ Available modes: {len([m for m in available_modes if m.get('available')])} of {len(available_modes)}")
    
    for mode in available_modes:
        status_icon = "✅" if mode.get("available") else "❌"
        auto_icon = "🤖" if mode.get("automated") else "👤"
        print(f"     {status_icon} {auto_icon} {mode.get('name', 'Unknown')}: {mode.get('description', 'No description')}")
    
    recommendations = status.get("recommendations", {})
    if recommendations:
        print(f"   💡 Primary recommendation: {recommendations.get('primary', 'N/A')}")
        print(f"   📋 Message: {recommendations.get('message', 'N/A')}")
    
    return True


def main():
    """Run all tests."""
    print("🔍 Testing pg_cron Cleanup Implementation")
    print("=" * 50)
    
    tests = [
        ("Cleanup Function Creation", test_cleanup_function),
        ("pg_cron Job Setup", test_pg_cron_setup),
        ("pg_cron Job Status", check_pg_cron_status),
        ("Manual Cleanup Test", test_manual_cleanup),
        ("Cleanup Statistics", get_cleanup_statistics),
        ("Cleanup Mode Status", test_cleanup_mode_status),
        ("Full Initialization", test_full_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! pg_cron cleanup is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())