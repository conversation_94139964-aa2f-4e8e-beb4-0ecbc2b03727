import os

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step50_cleanEnvironment_run(job_uuid, video_path):
    with StepExecutionContext(job_uuid, "cleanenv", logger):
        try:
            if os.path.exists(video_path):
                os.remove(video_path)
                logger.info(f"Successfully removed video file: {video_path}")
            else:
                logger.info(f"Video file not found, nothing to clean: {video_path}")
        except Exception as e:
            error_msg = f"Unexpected error while cleaning environment: {str(e)}"
            logger.error(error_msg)
            job_tracker.update_job_state(job_uuid, "cleanenv", "failed", error_msg)
            # Don't raise - return failed result instead to prevent job restart
            return
