import os

from app.config.logger_factory import get_logger
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step50_clean_environment_run(job_uuid, video_path):
    with StepExecutionContext(job_uuid, "cleanenv", logger):
        try:
            if os.path.exists(video_path):
                os.remove(video_path)
                logger.info(f"Successfully removed video file: {video_path}")
            else:
                logger.info(f"Video file not found, nothing to clean: {video_path}")
        except Exception as e:
            raise Exception(f"50|Unexpected error while cleaning environment: {str(e)}")
