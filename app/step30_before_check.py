import os

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker
from app.step31_objectDetection import step31_objectDetection_run
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step30_before_check_run(job_uuid, task_params_dict: dict, detection_file):
    try:
        with StepExecutionContext(job_uuid, "objectdetection", logger):
            if not os.path.isfile(detection_file):
                error_msg = f"File not found: {detection_file}"
                logger.error(error_msg)
                job_tracker.update_job_state(job_uuid, "objectdetection", "failed", error_msg)
                # Don't raise - return failed result instead to prevent job restart
                return

            step31_objectDetection_run(job_uuid, task_params_dict, detection_file)
    except Exception as e:
        error_msg = f"Object detection step failed: {str(e)}"
        logger.error(error_msg)
        job_tracker.update_job_state(job_uuid, "objectdetection", "failed", error_msg)
        # Don't raise - return failed result instead to prevent job restart
        return
