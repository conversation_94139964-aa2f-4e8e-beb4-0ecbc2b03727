import asyncio
import threading
import time
from typing import Dict

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.services.model_manager import model_manager

logger = get_logger(__name__)


class ModelPreloader:
    def __init__(self):
        self._preload_enabled = config.get("model_manager.preload_on_startup", True)
        self._health_check_enabled = config.get("health_check.enabled", True)
        health_check_minutes = config.get("health_check.interval_minutes", 60)
        self._health_check_interval = int(health_check_minutes) * 60 if health_check_minutes else 3600  # Convert to seconds
        self._cache_stats_enabled = config.get("monitoring.log_cache_stats", True)
        cache_stats_minutes = config.get("monitoring.cache_stats_interval_minutes", 30)
        self._cache_stats_interval = int(cache_stats_minutes) * 60 if cache_stats_minutes else 1800  # Convert to seconds
        self._health_check_thread = None
        self._stats_thread = None
        self._shutdown_event = threading.Event()

    def preload_models(self) -> bool:
        if not self._preload_enabled:
            logger.info("Model preloading is disabled")
            return True

        try:
            logger.info("Starting model preloading...")
            start_time = time.time()

            # Get models to preload from config
            preload_models = config.get("model_manager.preload_models", ["standard", "package"])
            
            # Ensure preload_models is a list
            if not isinstance(preload_models, list):
                logger.warning(f"preload_models config should be a list, got {type(preload_models)}")
                preload_models = ["standard", "package"]

            model_paths = {}
            for model_type in preload_models:
                model_path = config.get(f"{model_type}.model")
                if model_path:
                    model_paths[model_type] = model_path
                else:
                    logger.warning(f"No path configured for model type: {model_type}")

            if not model_paths:
                logger.warning("No models configured for preloading")
                return True

            # Preload models
            model_manager.preload_models(model_paths)

            total_time = time.time() - start_time
            logger.info(f"Model preloading completed in {total_time:.2f}s")

            # Log cache statistics
            stats = model_manager.get_cache_stats()
            logger.info(f"Preloaded {stats['cached_models']} models")

            return True

        except Exception as e:
            logger.error(f"Model preloading failed: {str(e)}")
            return False

    def start_background_services(self):
        if self._health_check_enabled:
            self._start_health_check_service()

        if self._cache_stats_enabled:
            self._start_cache_stats_service()

    def stop_background_services(self):
        logger.info("Stopping model preloader background services...")
        self._shutdown_event.set()

        if self._health_check_thread and self._health_check_thread.is_alive():
            self._health_check_thread.join(timeout=5)

        if self._stats_thread and self._stats_thread.is_alive():
            self._stats_thread.join(timeout=5)

    def _start_health_check_service(self):
        self._health_check_thread = threading.Thread(target=self._health_check_worker, name="ModelHealthCheck", daemon=True)
        self._health_check_thread.start()
        logger.info(f"Model health check service started (interval: {self._health_check_interval/60:.1f} minutes)")

    def _start_cache_stats_service(self):
        self._stats_thread = threading.Thread(target=self._cache_stats_worker, name="ModelCacheStats", daemon=True)
        self._stats_thread.start()
        logger.info(f"Cache statistics service started (interval: {self._cache_stats_interval/60:.1f} minutes)")

    def _health_check_worker(self):
        while not self._shutdown_event.wait(self._health_check_interval):
            try:
                self._perform_health_check()
            except Exception as e:
                logger.error(f"Health check failed: {str(e)}")

    def _cache_stats_worker(self):
        while not self._shutdown_event.wait(self._cache_stats_interval):
            try:
                self._log_cache_statistics()
            except Exception as e:
                logger.error(f"Cache stats logging failed: {str(e)}")

    def _perform_health_check(self):
        try:
            stats = model_manager.get_cache_stats()
            cached_models = stats.get("cached_models", 0)

            if cached_models == 0:
                logger.warning("No models in cache during health check")
                return

            # Check if models are still accessible
            health_issues = []
            for model_key, model_info in stats.get("models", {}).items():
                try:
                    # Simple accessibility check
                    model_path = model_info["path"]
                    if not model_path or not model_path.endswith(".pt"):
                        health_issues.append(f"Invalid model path: {model_path}")
                except Exception as e:
                    health_issues.append(f"Model {model_key}: {str(e)}")

            if health_issues:
                logger.warning(f"Model health check found issues: {health_issues}")
            else:
                logger.debug(f"Model health check passed ({cached_models} models)")

        except Exception as e:
            logger.error(f"Health check error: {str(e)}")

    def _log_cache_statistics(self):
        try:
            stats = model_manager.get_cache_stats()

            logger.info(f"Model Cache Stats - " f"Cached: {stats['cached_models']}/{stats['max_cache_size']}, " f"Device: {stats['device']}")

            # Log detailed model usage if debug logging is enabled
            # Note: Using try-catch to handle different logger implementations
            try:
                debug_enabled = hasattr(logger, 'isEnabledFor') and logger.isEnabledFor(10)  # DEBUG level
            except:
                debug_enabled = False
                
            if debug_enabled:
                for model_key, model_info in stats.get("models", {}).items():
                    logger.debug(f"Model {model_key}: " f"usage={model_info['usage_count']}, " f"last_used={time.time() - model_info['last_used']:.0f}s ago")

        except Exception as e:
            logger.error(f"Cache stats logging error: {str(e)}")



    def get_model_status(self) -> Dict:
        try:
            stats = model_manager.get_cache_stats()

            return {"preloader_enabled": self._preload_enabled, "health_check_enabled": self._health_check_enabled, "cache_stats": stats, "background_services": {"health_check_running": self._health_check_thread and self._health_check_thread.is_alive(), "stats_logging_running": self._stats_thread and self._stats_thread.is_alive()}}
        except Exception as e:
            logger.error(f"Error getting model status: {str(e)}")
            return {"error": str(e)}




# Global instance
model_preloader = ModelPreloader()


def initialize_models():
    try:
        # Preload models
        success = model_preloader.preload_models()
        if not success:
            logger.warning("Model preloading failed, but continuing...")

        # Start background services
        model_preloader.start_background_services()

        logger.info("Model initialization completed")
        return True

    except Exception as e:
        logger.error(f"Model initialization failed: {str(e)}")
        return False


def shutdown_models():
    try:
        model_preloader.stop_background_services()
        model_manager.clear_cache()
        logger.info("Model services shutdown completed")
    except Exception as e:
        logger.error(f"Model shutdown error: {str(e)}")


# FastAPI lifespan events
async def startup_event():
    logger.info("Starting model services...")
    await asyncio.get_event_loop().run_in_executor(None, initialize_models)


async def shutdown_event():
    logger.info("Shutting down model services...")
    await asyncio.get_event_loop().run_in_executor(None, shutdown_models)
