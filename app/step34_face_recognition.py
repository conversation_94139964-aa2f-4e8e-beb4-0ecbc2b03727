import os
import sys
import urllib

import face_recognition
import numpy
import requests
import supervision as sv

from app.config.logger_factory import get_logger
from app.helper import prepare_detection_result
from app.models import KnownPerson
from app.utils.logging_utils import filter_and_prepare_detection_result


def check_url_exists(url: str) -> bool:
    try:
        response = requests.head(url, timeout=5)
        return response.status_code == 200
    except requests.RequestException:
        return False


logger = get_logger(__name__)


def step34_face_recognition_run(
    job_uuid,
    detectionFile,
    face_recognition_known_people: list[KnownPerson],
    face_recognition_filter_items,
    confidenceThreshold,
    const_tolerance,
    facerec_skip_frames,
):
    known_faces = []
    known_values = []
    result = []
    tolerance = calc_tolerance(confidenceThreshold, const_tolerance)
    try:
        if os.path.isfile(detectionFile):
            if len(face_recognition_known_people) > 0:
                for people in face_recognition_known_people:

                    people_uuid = people.uuid
                    people_name = people.name
                    people_files = people.files

                    if len(people_files) > 0:
                        for people_file in people_files:
                            # people_file_uuid = people_file["uuid"]
                            people_file_meta = people_file.meta
                            people_photo = people_file_meta["url"]

                            existCheck = check_url_exists(people_photo)
                            if existCheck:
                                response = urllib.request.urlopen(people_photo)
                                lmm_image = face_recognition.load_image_file(response)
                                lmm_face_encoding_ary = face_recognition.face_encodings(lmm_image)
                                if len(lmm_face_encoding_ary) > 0:
                                    lmm_face_encoding = face_recognition.face_encodings(lmm_image)[0]

                                    known_faces.append(lmm_face_encoding)
                                    known_values.append(people_name)

                                    face_locations = []
                                    face_encodings = []
                                    for frame in sv.get_video_frames_generator(
                                        source_path=detectionFile,
                                        stride=facerec_skip_frames,
                                    ):
                                        rgb_frame = numpy.ascontiguousarray(frame[:, :, ::-1])

                                        face_locations = face_recognition.face_locations(rgb_frame)
                                        face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)

                                        for face_encoding in face_encodings:
                                            match = face_recognition.compare_faces(
                                                known_faces,
                                                face_encoding,
                                                tolerance=tolerance,
                                            )
                                            for k, m in enumerate(match):
                                                if m:
                                                    label = known_values[k]
                                                    result.append(label)
                                else:
                                    respmes = f"The operation could not be performed because the image you want to detect does not have the appropriate format/content. photo: {people_photo} uuid:{job_uuid}"
                                    logger.warning(respmes)
                            else:
                                respmes = f"Request status_code:{existCheck} photo: {people_photo} uuid:{job_uuid}"
                                logger.warning(respmes)
                    else:
                        respmes = f"There is no people information. uuid: {people_uuid} uuid:{job_uuid}"
                        logger.warning(respmes)

                if len(result) == 0:
                    prepare_detection_result(job_uuid, "unknownface", "knownpeople", "0")
                else:
                    result = set(result)
                    for result_people in result:
                        peopleuuid = fr_get_uuid_by_name(face_recognition_known_people, result_people)

                        filter_and_prepare_detection_result(job_uuid, result_people, "knownpeople", face_recognition_filter_items, peopleuuid)
    except Exception as e:
        raise Exception(f"34|Unexpected error while object detection err:{str(e)}")


def calc_tolerance(confidenceThreshold, const_tolerance):
    res = 1 - (const_tolerance * confidenceThreshold)
    return round(res, 2)


def fr_get_uuid_by_name(data: list[KnownPerson], name):
    try:
        for item in data:
            if item.name == name:
                return item.uuid
        return ""
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"fr_get_uuid_by_name Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
