from app.config.logger_factory import get_logger
from app.step31_object_detection import step31_object_detection_run
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step30_before_detection_run(job_uuid, task_params_dict: dict, detection_file):
    try:
        with StepExecutionContext(job_uuid, "objectdetection", logger):
            import cv2
            cap = cv2.VideoCapture(detection_file)
            video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            if video_width < 50 or video_height < 50:
                error_msg = f"Video width or height is too small: {video_width}x{video_height}"
                raise Exception(error_msg)

            video_fps = cap.get(cv2.CAP_PROP_FPS)
            if video_fps == 0:
                error_msg = f"Video fps is zero: {video_fps}"
                raise Exception(error_msg)

            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            if frame_count == 0:
                error_msg = f"Video frame count is zero: {frame_count}"
                raise Exception(error_msg)

            duration = round(frame_count / video_fps, 0)
            if duration < 5:
                error_msg = f"Video duration is too short: {duration}"
                raise Exception(error_msg)

            cap.release()

            task_params_dict['video_width'] = video_width
            task_params_dict['video_height'] = video_height
            task_params_dict['video_fps'] = video_fps
            task_params_dict['video_frame_count'] = frame_count
            task_params_dict['video_duration'] = duration

            step31_object_detection_run(job_uuid, task_params_dict, detection_file)
    except Exception as e:
        error_msg = f"30|Unexpected error while before object detection err:{str(e)}"
        raise Exception(error_msg)
