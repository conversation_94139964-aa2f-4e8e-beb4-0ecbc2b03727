"""
Database utilities for PostgreSQL operations including pg_cron job management.

This module provides utilities for:
- Setting up pg_cron scheduled jobs
- Creating SQL cleanup functions
- Managing database maintenance operations
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.database import db_manager

logger = get_logger(__name__)


class DatabaseUtils:
    """Utility class for database operations and maintenance tasks."""

    def __init__(self):
        self.db_manager = db_manager

    def create_cleanup_function(self) -> bool:
        """
        Create a PostgreSQL function that replicates the cleanup_expired_jobs logic.
        
        This function performs the same operations as the Python method:
        1. Deletes job_results for expired jobs
        2. Deletes job_steps for expired jobs  
        3. Deletes expired jobs themselves
        
        Returns:
            bool: True if function created successfully, False otherwise
        """
        # First drop the existing function if it exists
        drop_function_sql = "DROP FUNCTION IF EXISTS cleanup_expired_jobs();"

        cleanup_function_sql = """
        CREATE OR REPLACE FUNCTION cleanup_expired_jobs()
        RETURNS INTEGER AS $$
        DECLARE
            expired_count INTEGER := 0;
            job_uuids TEXT[];
        BEGIN
            -- Find expired job UUIDs for jobs that are completed or failed
            SELECT ARRAY(
                SELECT job_uuid
                FROM jobs
                WHERE expires_at < NOW() AT TIME ZONE 'UTC'
                AND state IN ('completed', 'failed')
            ) INTO job_uuids;

            -- Get count of jobs to be cleaned up
            expired_count := array_length(job_uuids, 1);

            -- Handle NULL case (no expired jobs)
            IF expired_count IS NULL THEN
                expired_count := 0;
            END IF;

            IF expired_count > 0 THEN
                -- Delete job_results first (foreign key constraint)
                DELETE FROM job_results
                WHERE job_uuid = ANY(job_uuids);

                -- Delete job_steps
                DELETE FROM job_steps
                WHERE job_uuid = ANY(job_uuids);

                -- Finally delete jobs
                DELETE FROM jobs
                WHERE job_uuid = ANY(job_uuids);

                -- Log the cleanup operation
                RAISE NOTICE 'Cleaned up % expired jobs from PostgreSQL at %',
                    expired_count, NOW() AT TIME ZONE 'UTC';
            END IF;

            RETURN expired_count;
        END;
        $$ LANGUAGE plpgsql;
        """

        try:
            with self.db_manager.session_scope() as session:
                # First drop the existing function
                try:
                    session.execute(text(drop_function_sql))
                    logger.info("Dropped existing cleanup_expired_jobs function")
                except SQLAlchemyError as e:
                    # Function might not exist - this is fine
                    logger.debug(f"Could not drop existing function (might not exist): {str(e)}")

                # Create the new function
                session.execute(text(cleanup_function_sql))
                logger.info("PostgreSQL cleanup function created successfully")
                return True
        except SQLAlchemyError as e:
            logger.error(f"Failed to create cleanup function: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating cleanup function: {str(e)}")
            return False

    def setup_pg_cron_job(self) -> bool:
        """
        Set up pg_cron job to run cleanup daily at 00:10.
        
        This creates a scheduled job that runs the cleanup_expired_jobs() function
        every day at 00:10 UTC.
        
        Returns:
            bool: True if pg_cron job set up successfully, False otherwise
        """
        # Check if pg_cron extension is available first
        if not self._check_pg_cron_availability():
            logger.warning("pg_cron extension is not available - skipping pg_cron job setup")
            return False

        # Create the cron job - runs daily at 00:10 UTC
        # Job name: 'cloudml_cleanup_expired_jobs'
        cron_job_sql = """
        SELECT cron.schedule(
            'cloudml_cleanup_expired_jobs',
            '10 0 * * *',
            'SELECT cleanup_expired_jobs();'
        );
        """

        try:
            with self.db_manager.session_scope() as session:
                # Remove existing job if it exists (to handle updates)
                try:
                    remove_job_sql = "SELECT cron.unschedule('cloudml_cleanup_expired_jobs');"
                    session.execute(text(remove_job_sql))
                    logger.info("Removed existing pg_cron job")
                except SQLAlchemyError:
                    # Job might not exist - this is fine
                    pass

                # Create the new cron job
                session.execute(text(cron_job_sql))
                logger.info("pg_cron job 'cloudml_cleanup_expired_jobs' scheduled to run daily at 00:10 UTC")
                return True

        except SQLAlchemyError as e:
            logger.error(f"Failed to setup pg_cron job: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error setting up pg_cron job: {str(e)}")
            return False

    def _check_pg_cron_availability(self) -> bool:
        """
        Check if pg_cron extension is available and enabled.
        
        Returns:
            bool: True if pg_cron is available, False otherwise
        """
        check_extension_sql = """
        SELECT EXISTS(
            SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
        ) OR EXISTS(
            SELECT 1 FROM pg_available_extensions WHERE name = 'pg_cron'
        );
        """
        
        try:
            with self.db_manager.read_only_session() as session:
                result = session.execute(text(check_extension_sql)).scalar()
                return bool(result)
        except SQLAlchemyError:
            # If we can't check, assume it's not available
            return False
        
    def _enable_pg_cron_extension(self) -> bool:
        """
        Try to enable pg_cron extension.
        
        Returns:
            bool: True if extension enabled successfully, False otherwise
        """
        enable_extension_sql = "CREATE EXTENSION IF NOT EXISTS pg_cron;"
        
        try:
            with self.db_manager.session_scope() as session:
                session.execute(text(enable_extension_sql))
                logger.info("pg_cron extension enabled successfully")
                return True
        except SQLAlchemyError as e:
            logger.warning(f"Could not enable pg_cron extension: {str(e)}")
            return False
        except Exception as e:
            logger.warning(f"Unexpected error enabling pg_cron extension: {str(e)}")
            return False

    def check_pg_cron_status(self) -> dict:
        """
        Check the status of the pg_cron job.
        
        Returns:
            dict: Status information about the pg_cron job
        """
        status_query = """
        SELECT 
            jobname,
            schedule,
            command,
            active,
            jobid
        FROM cron.job 
        WHERE jobname = 'cloudml_cleanup_expired_jobs';
        """

        try:
            with self.db_manager.read_only_session() as session:
                result = session.execute(text(status_query)).fetchone()
                
                if result:
                    return {
                        "job_exists": True,
                        "job_name": result.jobname,
                        "schedule": result.schedule,
                        "command": result.command,
                        "active": result.active,
                        "job_id": result.jobid
                    }
                else:
                    return {"job_exists": False}

        except SQLAlchemyError as e:
            logger.error(f"Failed to check pg_cron status: {str(e)}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error checking pg_cron status: {str(e)}")
            return {"error": str(e)}

    def manual_cleanup_test(self) -> dict:
        """
        Manually run the cleanup function for testing purposes.
        
        Returns:
            dict: Results of the manual cleanup operation
        """
        test_query = "SELECT cleanup_expired_jobs() as cleaned_count;"

        try:
            with self.db_manager.session_scope() as session:
                result = session.execute(text(test_query)).fetchone()
                cleaned_count = result.cleaned_count if result else 0
                
                return {
                    "success": True,
                    "cleaned_jobs": cleaned_count,
                    "timestamp": datetime.utcnow().isoformat()
                }

        except SQLAlchemyError as e:
            logger.error(f"Manual cleanup test failed: {str(e)}")
            return {"success": False, "error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error in manual cleanup test: {str(e)}")
            return {"success": False, "error": str(e)}

    def initialize_pg_cron_cleanup(self) -> bool:
        """
        Initialize the complete pg_cron cleanup system.
        
        This method:
        1. Creates the cleanup function
        2. Checks pg_cron availability
        3. Sets up the pg_cron job if available
        4. Provides fallback information if not available
        
        Returns:
            bool: True if basic setup successful (function created), False otherwise
        """
        logger.info("Initializing pg_cron cleanup system...")

        # Step 1: Create the cleanup function (always needed)
        if not self.create_cleanup_function():
            logger.error("Failed to create cleanup function")
            return False

        # Step 2: Check pg_cron availability (separate session to avoid transaction issues)
        pg_cron_available = self._check_pg_cron_availability()
        
        if not pg_cron_available:
            # Try to enable the extension (in a separate session)
            logger.info("Attempting to enable pg_cron extension...")
            if self._enable_pg_cron_extension():
                # Re-check availability after enabling
                pg_cron_available = self._check_pg_cron_availability()
            
        if pg_cron_available:
            # Step 3: Set up the cron job if pg_cron is available
            logger.info("pg_cron extension is available, setting up scheduled job...")
            if self.setup_pg_cron_job():
                # Step 4: Verify the setup
                status = self.check_pg_cron_status()
                if status.get("job_exists"):
                    logger.info("pg_cron cleanup system initialized successfully")
                    logger.info(f"Job details: {status}")
                    return True
                else:
                    logger.error("pg_cron job verification failed")
                    return False
            else:
                logger.error("Failed to setup pg_cron job")
                return False
        else:
            # pg_cron not available - provide fallback information
            logger.warning("pg_cron extension is not available on this PostgreSQL instance")
            logger.info("Cleanup function created successfully - you can use alternative scheduling methods:")
            logger.info("1. Manual cleanup via API: POST /cleanup/manual")
            logger.info("2. System cron job: Add to crontab to call the manual cleanup endpoint")
            logger.info("3. Application-level scheduler: Use APScheduler or similar")
            logger.info("4. Install pg_cron extension if you have database administrator privileges")
            
            # Return True because basic function setup succeeded
            return True

    def get_cleanup_statistics(self) -> dict:
        """
        Get statistics about jobs that would be cleaned up.
        
        Returns:
            dict: Statistics about expired jobs
        """
        stats_query = """
        WITH expired_jobs AS (
            SELECT 
                state,
                COUNT(*) as count,
                MIN(expires_at) as oldest_expiry,
                MAX(expires_at) as newest_expiry
            FROM jobs 
            WHERE expires_at < NOW() AT TIME ZONE 'UTC'
            AND state IN ('completed', 'failed')
            GROUP BY state
        )
        SELECT 
            state,
            count,
            oldest_expiry,
            newest_expiry
        FROM expired_jobs
        ORDER BY state;
        """

        try:
            with self.db_manager.read_only_session() as session:
                results = session.execute(text(stats_query)).fetchall()
                
                statistics = {
                    "total_expired": 0,
                    "by_state": {},
                    "oldest_expiry": None,
                    "newest_expiry": None
                }

                for row in results:
                    statistics["total_expired"] += row.count
                    statistics["by_state"][row.state] = {
                        "count": row.count,
                        "oldest_expiry": row.oldest_expiry.isoformat() if row.oldest_expiry else None,
                        "newest_expiry": row.newest_expiry.isoformat() if row.newest_expiry else None
                    }

                return statistics

        except SQLAlchemyError as e:
            logger.error(f"Failed to get cleanup statistics: {str(e)}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error getting cleanup statistics: {str(e)}")
            return {"error": str(e)}

    def get_cleanup_mode_status(self) -> dict:
        """
        Get comprehensive status of cleanup system including available modes.
        
        Returns:
            dict: Complete status including pg_cron availability and alternatives
        """
        try:
            pg_cron_available = self._check_pg_cron_availability()
            
            # Check if cleanup function exists
            function_exists = self._check_cleanup_function_exists()
            
            # Get pg_cron job status if available
            pg_cron_status = None
            if pg_cron_available:
                pg_cron_status = self.check_pg_cron_status()
            
            # Get cleanup statistics
            stats = self.get_cleanup_statistics()
            
            return {
                "cleanup_function_exists": function_exists,
                "pg_cron_available": pg_cron_available,
                "pg_cron_job_status": pg_cron_status,
                "cleanup_statistics": stats,
                "available_modes": self._get_available_cleanup_modes(pg_cron_available),
                "recommendations": self._get_cleanup_recommendations(pg_cron_available),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting cleanup mode status: {str(e)}")
            return {"error": str(e)}
    
    def _check_cleanup_function_exists(self) -> bool:
        """
        Check if the cleanup_expired_jobs function exists in the database.
        
        Returns:
            bool: True if function exists, False otherwise
        """
        check_function_sql = """
        SELECT EXISTS(
            SELECT 1 FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            WHERE p.proname = 'cleanup_expired_jobs' 
            AND n.nspname = 'public'
        );
        """
        
        try:
            with self.db_manager.read_only_session() as session:
                result = session.execute(text(check_function_sql)).scalar()
                return bool(result)
        except SQLAlchemyError:
            return False
    
    def _get_available_cleanup_modes(self, pg_cron_available: bool) -> list:
        """
        Get list of available cleanup modes based on system capabilities.
        
        Args:
            pg_cron_available: Whether pg_cron extension is available
            
        Returns:
            list: Available cleanup modes
        """
        modes = [
            {
                "mode": "manual_api",
                "name": "Manual API Cleanup",
                "description": "Trigger cleanup via POST /cleanup/manual endpoint",
                "available": True,
                "automated": False
            },
            {
                "mode": "system_cron",
                "name": "System Cron Job",
                "description": "Use system crontab to call manual cleanup endpoint",
                "available": True,
                "automated": True
            },
            {
                "mode": "application_scheduler",
                "name": "Application Scheduler",
                "description": "Use Python APScheduler or similar within the application",
                "available": True,
                "automated": True
            }
        ]
        
        if pg_cron_available:
            modes.insert(0, {
                "mode": "pg_cron",
                "name": "PostgreSQL pg_cron",
                "description": "Database-native scheduled cleanup (recommended)",
                "available": True,
                "automated": True
            })
        else:
            modes.insert(0, {
                "mode": "pg_cron",
                "name": "PostgreSQL pg_cron",
                "description": "Database-native scheduled cleanup (requires pg_cron extension)",
                "available": False,
                "automated": True
            })
        
        return modes
    
    def _get_cleanup_recommendations(self, pg_cron_available: bool) -> dict:
        """
        Get recommendations for cleanup setup based on system capabilities.
        
        Args:
            pg_cron_available: Whether pg_cron extension is available
            
        Returns:
            dict: Cleanup recommendations
        """
        if pg_cron_available:
            return {
                "primary": "pg_cron",
                "message": "pg_cron is available and recommended for automated cleanup",
                "setup_required": False
            }
        else:
            return {
                "primary": "system_cron",
                "message": "pg_cron not available. Recommended: Use system cron job to call manual cleanup endpoint",
                "setup_required": True,
                "installation_guide": {
                    "pg_cron_install": "To install pg_cron: 1) Install extension package 2) Add 'shared_preload_libraries = pg_cron' to postgresql.conf 3) Restart PostgreSQL 4) CREATE EXTENSION pg_cron;",
                    "system_cron_example": "Add to crontab: 10 0 * * * curl -X POST http://localhost:8080/cleanup/manual -H 'Authorization: Bearer YOUR_TOKEN'"
                }
            }


# Global instance
database_utils = DatabaseUtils()