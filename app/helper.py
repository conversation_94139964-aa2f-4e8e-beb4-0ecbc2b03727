import json
import os
import sys
import time
import uuid
from datetime import datetime

import requests

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.job_tracking import job_tracker
from app.models import BBox, DetectionData, HotZone

logger = get_logger(__name__)


def get_object_label_uuid(label, detectedClass):
    static_labels_uuid = {
        "unknownface": "00000000-0000-0000-0000-000000000001",
        "person": "00000000-0000-0000-0000-000000000002",
        "car": "00000000-0000-0000-0000-000000000003",
        "bicycle": "00000000-0000-0000-0000-000000000004",
        "motorcycle": "00000000-0000-0000-0000-000000000005",
        "bus": "00000000-0000-0000-0000-000000000006",
        "truck": "00000000-0000-0000-0000-000000000007",
        "bird": "00000000-0000-0000-0000-000000000008",
        "cat": "00000000-0000-0000-0000-000000000009",
        "dog": "00000000-0000-0000-0000-000000000010",
        "horse": "00000000-0000-0000-0000-000000000011",
        "sheep": "00000000-0000-0000-0000-000000000012",
        "cow": "00000000-0000-0000-0000-000000000013",
        "elephant": "00000000-0000-0000-0000-000000000014",
        "bear": "00000000-0000-0000-0000-000000000015",
        "zebra": "00000000-0000-0000-0000-000000000016",
        "giraffe": "00000000-0000-0000-0000-000000000017",
        "train": "00000000-0000-0000-0000-000000000018",
        "boat": "00000000-0000-0000-0000-000000000019",
        "box": "00000000-0000-0000-0000-000000000020",
        "package": "00000000-0000-0000-0000-000000000021",
        "parcel": "00000000-0000-0000-0000-000000000022",
        "book": "00000000-0000-0000-0000-000000000023",
        "knowpeople": "00000000-0000-0000-0000-000000000024",
        "licenseplate": "00000000-0000-0000-0000-000000000025",
        "barcode": "00000000-0000-0000-0000-000000000026",
    }

    if label in static_labels_uuid:
        return static_labels_uuid[label]
    else:
        if detectedClass in static_labels_uuid:
            return static_labels_uuid[detectedClass]
        else:
            return "00000000-0000-0000-0000-000000000000"


def prepare_hot_zone(uuid, hotzone_zones: list[HotZone], video_width, video_height):
    result = []
    # If there is no maxwidth, maxheight information in the payload data, 1920x1080 is accepted.
    defaultMaxWidth = 1920
    defaultMaxHeight = 1080

    for hzone in hotzone_zones:
        zone = hzone.model_dump()
        jdZone = json.dumps(zone)
        jZone = json.loads(jdZone)

        jMaxWidth = 0  # payload X value
        jMaxHeight = 0  # payload Y value
        points = []  # payload points value

        if "maxwidth" in jZone:
            jMaxWidth = int(jZone["maxwidth"])

        if "maxheight" in jZone:
            jMaxHeight = int(jZone["maxheight"])

        if jMaxWidth == 0 or jMaxHeight == 0:
            jMaxWidth = defaultMaxWidth
            jMaxHeight = defaultMaxHeight

        if "points" in jZone:
            points = jZone["points"]
            # [{'x': 500, 'y': 500}, {'x': 600, 'y': 600}, {'x': 700, 'y': 500}, {'x': 600, 'y': 400}]

        numpyList = []
        for coord in points:
            # coord: {'x': 100, 'y': 100}
            jdCoord = json.dumps(coord)
            jCoord = json.loads(jdCoord)
            valX = 0
            valY = 0

            if "x" not in jCoord or "y" not in jCoord:
                logger.debug("there is no x or y information in the payload data. uuid:%s", uuid)
            else:
                if "x" in jCoord:
                    valX = int(jCoord["x"])

                if "y" in jCoord:
                    valY = int(jCoord["y"])

                if valX < 0 or valY < 0:
                    logger.debug("x or y information must be greater than 0.uuid:%s", uuid)
                else:
                    if valX > jMaxWidth or valY > jMaxHeight:
                        logger.debug(
                            "x or y information must be less than maxwidth or maxheight.uuid:%s",
                            uuid,
                        )
                    else:
                        if jMaxWidth == video_width and jMaxHeight == video_height:
                            numpyList.append([valX, valY])
                        else:
                            ratioX = video_width / jMaxWidth
                            ratioY = video_height / jMaxHeight

                            newValX = round(valX * ratioX, 0)
                            newValY = round(valY * ratioY, 0)

                            newValX = int(newValX)
                            newValY = int(newValY)

                            numpyList.append([newValX, newValY])

        logger.debug("numpyList: %s uuid:%s", numpyList, uuid)
        result.append(numpyList)

    return result


def prepare_detection_result(uuid, label, detectedClass, people_uuid=None):
    try:
        label_element_uuid = get_object_label_uuid(label, detectedClass)

        if people_uuid is None:
            detection_data = DetectionData(
                label=label,
                label_element=label_element_uuid,
                timestamp=round(time.time()),
                milisec=round(datetime.now().microsecond / 1000),
                class_=detectedClass,
                conf=0,
                bbox=BBox(xmin=0, ymin=0, xmax=0, ymax=0),
            )
        else:
            detection_data = DetectionData(
                label=label,
                label_element=label_element_uuid,
                known_people_uuid=people_uuid,
                timestamp=round(time.time()),
                milisec=round(datetime.now().microsecond / 1000),
                class_=detectedClass,
                conf=0,
                bbox=BBox(xmin=0, ymin=0, xmax=0, ymax=0),
            )

        detection_data_json = detection_data.model_dump(exclude_none=True, by_alias=True)
        job_tracker.create_job_result(uuid, detection_data_json)
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"prepare_detection_result Exception with error: {e} file:{fname} line:{exc_tb.tb_lineno} uuid:{uuid} exc_info:{sys.exc_info}")


def find_class(value, personClasses=None, vehicleClasses=None, animalClasses=None):
    if animalClasses is None:
        animalClasses = []
    if vehicleClasses is None:
        vehicleClasses = []
    if personClasses is None:
        personClasses = []

    mlmodel_classes = {
        "person": personClasses,
        "vehicle": vehicleClasses,
        "animal": animalClasses,
    }

    for category, items in mlmodel_classes.items():
        if value in items:
            return category
    return None


def hotzone_video_info(uuid: str, hotzone_zones: list[HotZone], video_width: int, video_height: int):
    try:
        import numpy as np
        import supervision as sv

        hotzone_zones_polygon = []
        resultHotZone = []
        polygons = []
        if isinstance(hotzone_zones, list):
            if len(hotzone_zones) > 0:
                resultHotZone = prepare_hot_zone(uuid, hotzone_zones, video_width, video_height)

        if len(resultHotZone) > 0:
            for polygon in resultHotZone:
                if len(polygon) > 0:
                    nmPolygon = np.array(polygon)
                    polygons.append(nmPolygon)

            hotzone_zones_polygon = [sv.PolygonZone(polygon=polygon) for polygon in polygons]

        return hotzone_zones_polygon
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"hotzone_video_info Exception with error: {e} file:{fname} line:{exc_tb.tb_lineno} uuid:{uuid} exc_info:{sys.exc_info}")


def skip_factor_check(fvalue):
    try:
        fvaluec = float(fvalue)
        if 0.0 <= fvaluec <= 1.0:
            return fvaluec
        else:
            return 0
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return (
            False,
            f"skip_factor_check Exception with error: {err} uuid:{uuid} fvalue:{fvalue} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}",
        )


def get_skip_frames(video_fps, skip_factor):
    skip_factor = skip_factor_check(skip_factor)
    standard_skip_frames = max(1, int(video_fps * skip_factor) - 1)
    if standard_skip_frames < 0:
        standard_skip_frames = 1
    return standard_skip_frames


def get_yolo_model_classindex(model, predict_class_name):
    cnames = model.names
    mclasses = []
    for i in range(len(predict_class_name)):
        for k in range(len(cnames)):
            if cnames[k] == predict_class_name[i]:
                mclasses.append(k)
                break
    return mclasses


def get_standard_class_index(
    model,
    person_detection,
    person_class,
    vehicle_detection,
    vehicle_class,
    animal_detection,
    animal_class,
):
    try:
        standard_classes = ""
        if person_detection:
            person_class_str = ", ".join(person_class)
            standard_classes = standard_classes + "," + person_class_str

        if animal_detection:
            animal_class_str = ", ".join(animal_class)
            standard_classes = standard_classes + "," + animal_class_str

        if vehicle_detection:
            vehicle_class_str = ", ".join(vehicle_class)
            standard_classes = standard_classes + "," + vehicle_class_str

        ary_standard_class = [item for item in standard_classes.split(",") if item]
        standard_class_index = get_yolo_model_classindex(model, ary_standard_class)

        return standard_class_index
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        respmes = f"get_standard_class_index Exception with error: {err} uuid:{uuid} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        logger.error(respmes)


def get_package_class(package_class):
    return [str(item).strip() for item in package_class if item]


def get_package_confidence_threshold(confidence_threshold):
    package_convert_rate = 0.69
    result = confidence_threshold * package_convert_rate
    return round(result, 2)


def typed_append(tup, item, item_type=None):
    if item_type is None:
        if len(tup) == 0:
            item_type = type(item)
        else:
            item_type = type(tup[0])

    if not isinstance(item, item_type):
        pass

    return tup + (item,)


def find_existing(base_str, values_list):
    try:
        base_items = [str(x).strip() for x in base_str]
        found = [v for v in values_list if v in base_items]
        return found
    except Exception as err:
        import sys

        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        respmes = f"find_existing Exception with error: {err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        logger.error(respmes)


def send_open_telemetry(
    trace_id,
    parent_span_id,
    status_code,
    status_message,
    scope_name,
    scope_attributes,
    scope_values,
    span_name,
    span_attributes,
    span_values,
    startTimeUnixNano,
):
    try:
        import secrets

        span_id = secrets.token_hex(8).upper()

        version = get_app_version()

        if startTimeUnixNano == 0:
            endTimeUnixNano = 0
        else:
            endTimeUnixNano = time.time_ns()

        send_span_attributes = []
        if span_attributes is not None:
            for i in range(len(span_attributes)):
                send_span_attributes.append(
                    {
                        "key": str(span_attributes[i]),
                        "value": {"stringValue": str(span_values[i])},
                    }
                )

        send_scope_attributes = []
        if scope_attributes is not None:
            for i in range(len(scope_attributes)):
                send_scope_attributes.append(
                    {
                        "key": str(scope_attributes[i]),
                        "value": {"stringValue": str(scope_values[i])},
                    }
                )

        trace_json = {
            "resourceSpans": [
                {
                    "resource": {"attributes": [{"key": "service.name", "value": {"stringValue": "CLOUDML"}}]},
                    "scopeSpans": [
                        {
                            "scope": {
                                "name": scope_name,
                                "version": version,
                                "attributes": send_scope_attributes,
                            },
                            "spans": [
                                {
                                    "traceId": trace_id,
                                    "spanId": span_id,
                                    "parentSpanId": parent_span_id,
                                    "name": span_name,
                                    "startTimeUnixNano": str(startTimeUnixNano),
                                    "endTimeUnixNano": str(endTimeUnixNano),
                                    "kind": 2,
                                    "attributes": send_span_attributes,
                                    "status": {
                                        "code": status_code,
                                        "message": str(status_message),
                                    },
                                }
                            ],
                        }
                    ],
                }
            ]
        }
        jsonResult = json.dumps(trace_json)
        logger.debug(jsonResult)

        url = config.get("open_telemetry.collector_endpoint", "https://ot.ezlo.com/traces")
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, data=jsonResult, headers=headers)
        if response.ok:
            respmes = f"send_open_telemetry sending result to: {url}, jsonResult: {jsonResult} status_code:{response.status_code}  span_id:{span_id}"
            logger.debug(respmes)
        else:
            respmes = f"send_open_telemetry could not be sent detected objects: {jsonResult} were not sent to: {url} text:{response.text} reason:{response.reason} status_code:{response.status_code} span_id:{span_id}"
            logger.error(respmes)
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"send_open_telemetry Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        logger.error(err_msg)


def get_total_results(found_classes, results):
    try:
        if results is not None:
            for skey in results:
                sconf = results[skey]
                if skey in found_classes:
                    if sconf > found_classes[skey]:
                        found_classes[skey] = sconf
                else:
                    found_classes[skey] = sconf

        found_classes_converted = {str(k): round(v, 2) for k, v in found_classes.items()}
        return found_classes_converted
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"get_total_results Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)


def get_app_version():
    # major.minor.commitIndex
    VERSION = "1.0.0"
    return VERSION
