from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.database import BaseRepository, Job, JobResult, JobStep, db_manager, JobCounts

logger = get_logger(__name__)


class JobTracker(BaseRepository):
    def __init__(self):
        super().__init__(db_manager)
        self._init_postgresql()

    def _init_postgresql(self):
        try:
            logger.info("PostgreSQL job tracker initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL job tracker: {str(e)}")
            raise

    def job_exists(self, uuid: str) -> bool:
        try:
            with self.get_read_session() as session:
                # Use exists() for better performance instead of first()
                from sqlalchemy import exists
                result = session.query(exists().where(Job.job_uuid == uuid)).scalar()
                return result
        except SQLAlchemyError as e:
            logger.error(f"Error checking job existence for UUID {uuid}: {str(e)}")
            return True  # Return True to prevent duplicate creation on error

    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str, task_id: Optional[str] = None, worker_id: Optional[str] = None, hostname: Optional[str] = None) -> bool:
        try:
            if self.job_exists(uuid):
                logger.warning(f"Job with UUID {uuid} already exists")
                # Increment rejected counter when job already exists
                db_manager.increment_job_counter("rejected")
                return False

            retention_days = config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)

            with self.get_session() as session:
                job = Job(
                    job_uuid=uuid,
                    state="queued",
                    task_id=task_id,
                    worker_id=worker_id,
                    hostname=hostname,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    expires_at=expires_at,
                    ot_traceid=ot_traceid,
                    ot_spanid=ot_spanid,
                )
                session.add(job)
                # Session automatically commits due to context manager

            # Increment accepted counter
            db_manager.increment_job_counter("accepted")

            logger.info(f"Job {uuid} created successfully with task_id: {task_id}")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating job {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating job {uuid}: {str(e)}")
            return False

    def create_job_result(self, job_uuid: str, result: dict) -> bool:
        try:
            with self.get_session() as session:
                # First check if job exists
                job = session.query(Job).filter(Job.job_uuid == job_uuid).first()
                if not job:
                    logger.warning(f"Job {job_uuid} not found, cannot insert result")
                    return False

                job_result = JobResult(job_uuid=job_uuid, result=result, created_at=datetime.utcnow())
                session.add(job_result)
                # Session automatically commits due to context manager

            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating JobResult for {job_uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating JobResult for {job_uuid}: {str(e)}")
            return False

    def create_job_step(self, job_uuid: str, step: str, state: str, error_message: Optional[str] = None) -> bool:
        """Create a job step record to track all job state updates"""
        try:
            with self.get_session() as session:
                job_step = JobStep(
                    job_uuid=job_uuid,
                    step=step,
                    state=state,
                    error_message=error_message,
                    created_at=datetime.utcnow(),
                )
                session.add(job_step)
                # Session automatically commits due to context manager

            logger.debug(f"Job step record created for {job_uuid}: step={step}, state={state}")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating JobStep for {job_uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating JobStep for {job_uuid}: {str(e)}")
            return False

    def update_job_state(
        self,
        uuid: str,
        step: str,
        state: str,
        error_message: Optional[str] = None,
        task_id: Optional[str] = None,
        worker_id: Optional[str] = None,
        hostname: Optional[str] = None,
    ) -> bool:
        try:
            with self.get_session() as session:
                job = session.query(Job).filter(Job.job_uuid == uuid).first()
                if not job:
                    logger.warning(f"Job {uuid} not found for state update")
                    return False

                job.state = state
                job.step = step
                job.updated_at = datetime.utcnow()
                if task_id:
                    job.task_id = task_id
                if worker_id:
                    job.worker_id = worker_id
                if hostname:
                    job.hostname = hostname

                # Debug logging for error_message updates
                if error_message:
                    logger.debug(f"Setting error_message for job {uuid}: {error_message[:100]}{'...' if len(str(error_message)) > 100 else ''}")

                # Explicitly flush changes to ensure they're written
                session.flush()
                # Session automatically commits due to context manager

            # Increment counters based on state changes
            if state == "failed":
                db_manager.increment_job_counter("failed")
            elif state == "completed":
                db_manager.increment_job_counter("completed")

            # Create job step record for audit trail (separate transaction)
            self.create_job_step(
                job_uuid=uuid,
                step=step,
                state=state,
                error_message=error_message
            )

            return True
        except SQLAlchemyError as e:
            logger.error(f"Error updating job state for {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating job state for {uuid}: {str(e)}")
            return False

    def get_job(self, uuid: str) -> Optional[Dict[str, Any]]:
        try:
            with self.get_read_session() as session:
                job = session.query(Job).filter(Job.job_uuid == uuid).first()
                if job:
                    return {
                        "uuid": job.job_uuid,
                        "state": job.state,
                        "step": job.step,
                        "task_id": job.task_id,
                        "worker_id": job.worker_id,
                        "hostname": job.hostname,
                        "created_at": job.created_at,
                        "updated_at": job.updated_at,
                        "expires_at": job.expires_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                    }
                return None
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving job {uuid}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving job {uuid}: {str(e)}")
            return None

    def get_job_state_by_uuid(self, job_uuid: str) -> Optional[str]:
        try:
            with self.get_read_session() as session:
                stmt = select(Job.state).where(Job.job_uuid == job_uuid)
                row = session.execute(stmt).scalar_one_or_none()
                return row  # state string or None
        except SQLAlchemyError as e:
            logger.error(f"Error fetching state for job {job_uuid}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching state for job {job_uuid}: {str(e)}")
            return None

    def get_jobs_by_state(self, state: str) -> List[Dict[str, Any]]:
        try:
            with (self.get_read_session() as session):
                jobs = (session.query(Job).filter(Job.state == state).order_by(Job.id.desc()).limit(10).all())
                return [
                    {
                        "uuid": job.job_uuid,
                        "state": job.state,
                        "task_id": job.task_id,
                        "created_at": job.created_at,
                        "ot_traceid": job.ot_traceid,
                        "ot_spanid": job.ot_spanid,
                    }
                    for job in jobs
                ]
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving jobs with state {state}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving jobs with state {state}: {str(e)}")
            return []

    def get_job_results(self, job_uuid: str) -> List[Dict[str, Any]]:
        try:
            with self.get_read_session() as session:
                # Get results with latest error message from job_steps
                stmt = (
                    select(
                        Job.state,
                        JobResult.result,
                        JobResult.result["class"].astext.label("class"),
                    )
                    .join(JobResult, Job.job_uuid == JobResult.job_uuid)
                    .where(Job.job_uuid == job_uuid)
                )
                rows = session.execute(stmt).all()

                if not rows:
                    logger.info(f"No results found for job {job_uuid}")
                    return []

                # Get latest error message from job_steps if any
                latest_error_msg = None
                error_step = session.query(JobStep).filter(
                    JobStep.job_uuid == job_uuid,
                    JobStep.error_message.isnot(None)
                ).order_by(JobStep.created_at.desc()).first()

                if error_step:
                    latest_error_msg = error_step.error_message

                results = [
                    {
                        "state": state,
                        "result": result,
                        "error_message": latest_error_msg,
                        "class": class_name,
                    }
                    for state, result, class_name in rows
                ]
                return results

        except SQLAlchemyError as e:
            logger.error(f"Error fetching job results for {job_uuid}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching job results for {job_uuid}: {str(e)}")
            return []

    def get_job_steps(self, job_uuid: str) -> List[Dict[str, Any]]:
        """Get all job step records for a specific job UUID"""
        try:
            with self.get_read_session() as session:
                job_steps = session.query(JobStep).filter(JobStep.job_uuid == job_uuid).order_by(JobStep.created_at.asc()).all()
                return [
                    {
                        "id": job_step.id,
                        "job_uuid": job_step.job_uuid,
                        "step": job_step.step,
                        "state": job_step.state,
                        "error_message": job_step.error_message,
                        "created_at": job_step.created_at,
                    }
                    for job_step in job_steps
                ]
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving job steps for {job_uuid}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving job steps for {job_uuid}: {str(e)}")
            return []

    def get_job_count_by_state(self, state: str) -> Dict[str, Any]:
        try:
            with (self.get_read_session() as session):
                jobCounts = (session.query(JobCounts).filter(JobCounts.id == 1).first())
                return {
                    "accepted": jobCounts.accepted,
                    "successful": jobCounts.successful,
                    "failed": jobCounts.failed,
                    "completed": jobCounts.completed,
                    "rejected": jobCounts.rejected
                }
        except SQLAlchemyError as e:
            logger.error(f"Error counting jobs with state {state}: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error counting jobs with state {state}: {str(e)}")
            return {}

    def get_job_count_by_queue(self) -> int:
        try:
            with self.get_read_session() as session:
                return session.query(Job).filter(Job.state == "queued").count()
        except SQLAlchemyError as e:
            logger.error(f"Error counting jobs with state queued: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error counting jobs with state queued: {str(e)}")
            return 0

    def cleanup_expired_jobs(self) -> int:
        try:
            with self.get_session() as session:
                current_time = datetime.utcnow()

                # Use bulk delete for better performance
                from sqlalchemy import and_

                # First get count for logging
                count_query = session.query(Job).filter(
                    and_(
                        Job.expires_at < current_time,
                        Job.state.in_(['completed', 'failed'])  # Only cleanup finished jobs
                    )
                )
                count = count_query.count()

                if count > 0:
                    # Delete job_results first (foreign key constraint)
                    session.query(JobResult).filter(
                        JobResult.job_uuid.in_(
                            session.query(Job.job_uuid).filter(
                                and_(
                                    Job.expires_at < current_time,
                                    Job.state.in_(['completed', 'failed'])
                                )
                            )
                        )
                    ).delete(synchronize_session=False)

                    # Delete job_steps
                    session.query(JobStep).filter(
                        JobStep.job_uuid.in_(
                            session.query(Job.job_uuid).filter(
                                and_(
                                    Job.expires_at < current_time,
                                    Job.state.in_(['completed', 'failed'])
                                )
                            )
                        )
                    ).delete(synchronize_session=False)

                    # Finally delete jobs
                    session.query(Job).filter(
                        and_(
                            Job.expires_at < current_time,
                            Job.state.in_(['completed', 'failed'])
                        )
                    ).delete(synchronize_session=False)

                    logger.info(f"Cleaned up {count} expired jobs from PostgreSQL")

                return count
        except SQLAlchemyError as e:
            logger.error(f"Error cleaning up expired jobs: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error cleaning up expired jobs: {str(e)}")
            return 0


job_tracker = JobTracker()
