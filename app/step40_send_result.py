import json
from typing import Optional

import requests
from requests.exceptions import (<PERSON><PERSON>rror, HTTPError, RequestException,
                                 Timeout)

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker
from app.models import TaskParameters
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def send_result(callback_url: str, json_data: str):
    try:
        headers = {"Content-Type": "application/json"}
        response = requests.post(callback_url, data=json_data, headers=headers)
        if response.ok:
            logger.debug(
                f"sending result to: {callback_url}, jsonResult: {json_data} status_code:{response.status_code}")
        else:
            error_msg = f"could not be sent detected objects: {json_data} were not sent to: {callback_url} text:{response.text} reason:{response.reason} status_code:{response.status_code}"
            raise Exception(error_msg)
    except Timeout as e:
        error_msg = f"Failed to send results to callback timeout {str(e)}"
        raise Exception(error_msg)
    except ConnectionError as e:
        error_msg = f"Failed to send results to callback connection error: {str(e)}"
        raise Exception(error_msg)
    except HTTPError as e:
        error_msg = f"Failed to send results to callback http error: {str(e)}"
        raise Exception(error_msg)
    except RequestException as e:
        error_msg = f"Failed to send results to callback request exception: {str(e)}"
        raise Exception(error_msg)


def step40_send_result_run(job_uuid: str, task_params: TaskParameters, job_state, error_msg: Optional[str] = None):
    with StepExecutionContext(job_uuid, "sendresult", logger):
        try:
            if job_state == "failed":
                json_data_detail = {"uuid": job_uuid, "message": error_msg}
                json_data = {"status": 0, "error_code": 52323, "error_detail": json_data_detail}
                jsonResult = json.dumps(json_data)
                send_result(task_params.response.callback, jsonResult)

            rows = job_tracker.get_job_results(job_uuid)
            detectedData = []
            for rec in rows:
                if job_state is None:
                    job_state = rec["state"]

                if error_msg is None:
                    error_msg = rec["error_message"]

                result = rec["result"]
                class_value = rec["class"]

                payload = {
                    "uuid": str(job_uuid),
                    "type": str(class_value),
                    "data": result,
                }

                detectedData.append(payload)

            responseJson = {
                "ot": {
                    "trace_id": str(task_params.ot.trace_id),
                    "span_id": str(task_params.ot.span_id),
                },
                "status": 1,
                "detected": detectedData,
                "uuid": job_uuid,
            }
            jsonResult = json.dumps(responseJson)
            send_result(task_params.response.callback, jsonResult)
        except Exception as e:
            raise Exception(f"40|Unexpected error while sending results to callback url:{task_params.response.callback} err:{str(e)}")
