from contextlib import contextmanager
from datetime import datetime
from typing import Generator

from sqlalchemy import BigInteger, Column, DateTime, ForeignKey, Integer, String, Text, create_engine
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship, sessionmaker

from app.config.logger_factory import get_logger
from app.config.settings import config

logger = get_logger(__name__)

Base = declarative_base()


class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    job_uuid = Column(String(36), unique=True, nullable=False, index=True)
    state = Column(String(50), nullable=False, index=True)
    step = Column(String(50), nullable=True)
    task_id = Column(String(255), nullable=True, index=True)
    worker_id = Column(String(255), nullable=True, index=True)
    hostname = Column(String(255), nullable=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False, index=True)
    ot_traceid = Column(String(255), nullable=True, index=True)
    ot_spanid = Column(String(255), nullable=True)

    results = relationship("JobResult", back_populates="job", lazy="select")

    def __repr__(self):
        return f"<Job(job_uuid='{self.job_uuid}', state='{self.state}', step='{self.step}')>"


class JobResult(Base):
    __tablename__ = "job_results"

    id = Column(Integer, primary_key=True, index=True)
    job_uuid = Column(String(36), ForeignKey("jobs.job_uuid"), nullable=False, index=True)
    result = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    job = relationship("Job", back_populates="results", lazy="select")


class JobStep(Base):
    __tablename__ = "job_steps"

    id = Column(Integer, primary_key=True, autoincrement=True)
    job_uuid = Column(String(36), ForeignKey("jobs.job_uuid"), nullable=False, index=True)
    step = Column(String(50), nullable=False)
    state = Column(String(50), nullable=False, index=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    job = relationship("Job", lazy="select")

    def __repr__(self):
        return f"<JobStep(job_uuid='{self.job_uuid}', step='{self.step}', state='{self.state}')>"


class JobCounts(Base):
    __tablename__ = "job_counts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    accepted = Column(BigInteger, nullable=False, default=0)
    successful = Column(BigInteger, nullable=False, default=0)
    failed = Column(BigInteger, nullable=False, default=0)
    completed = Column(BigInteger, nullable=False, default=0)
    rejected = Column(BigInteger, nullable=False, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<JobCounts(accepted={self.accepted}, successful={self.successful}, failed={self.failed}, completed={self.completed}, rejected={self.rejected})>"


class DatabaseManager:
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()

    def _init_database(self):
        """Initialize basic database connection only"""
        try:
            host = config.get("postgresql.host", "postgresql")
            port = config.get("postgresql.port", 5432)
            username = config.get("postgresql.username", "postgres")
            password = config.get("postgresql.password", "password")
            database = config.get("postgresql.database", "object_detection")
            echo_value = config.get("postgresql.echo", False)

            connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"

            # Optimized connection pool settings for performance
            self.engine = create_engine(
                connection_string,
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=1800,   # Recycle connections every 30 minutes
                echo=echo_value,
                pool_size=15,        # Increased pool size for better concurrency
                max_overflow=25,     # Higher overflow for peak loads
                pool_timeout=30,     # Timeout for getting connection from pool
                isolation_level="READ COMMITTED",
                connect_args={
                    "application_name": "cloudml_v3"
                }
            )

            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            # Only create tables on startup - critical operations moved to init_critical_operations()
            Base.metadata.create_all(bind=self.engine)

            logger.info("PostgreSQL database manager basic connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL database manager: {str(e)}")
            raise

    def init_critical_operations(self):
        """Initialize critical database operations like indexes and job counts"""
        try:
            logger.info("Starting critical database operations initialization...")

            # Initialize job counts table with singleton record
            self._initialize_job_counts()

            logger.info("Critical database operations completed successfully")
            return {"status": "success", "message": "Critical database operations completed successfully"}
        except Exception as e:
            error_msg = f"Failed to complete critical database operations: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def _initialize_job_counts(self):
        """Initialize job_counts table with a singleton record if it doesn't exist"""
        try:
            with self.session_scope() as session:
                # Check if job_counts record exists
                existing_count = session.query(JobCounts).first()
                if not existing_count:
                    # Create the initial record with all counters at 0
                    job_counts = JobCounts(
                        accepted=0,
                        successful=0,
                        failed=0,
                        completed=0,
                        rejected=0
                    )
                    session.add(job_counts)
                    logger.info("Job counts table initialized with default values")
                else:
                    logger.info("Job counts table already exists")
        except Exception as e:
            logger.error(f"Failed to initialize job counts table: {str(e)}")



    def get_session(self) -> Session:
        return self.SessionLocal()

    @contextmanager
    def session_scope(self, auto_commit: bool = True) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
            if auto_commit:
                session.commit()
                logger.debug("Database transaction committed successfully")
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Database error occurred, rolling back transaction: {str(e)}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"Unexpected error occurred, rolling back transaction: {str(e)}")
            raise
        finally:
            session.close()
            logger.debug("Database session closed")

    @contextmanager
    def read_only_session(self) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
        except SQLAlchemyError as e:
            logger.error(f"Database error during read operation: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during read operation: {str(e)}")
            raise
        finally:
            session.rollback()  # Ensure no changes are committed for read-only operations
            session.close()
            logger.debug("Read-only database session closed")

    def close(self):
        if self.engine:
            self.engine.dispose()
            logger.info("Database engine disposed successfully")

    def increment_job_counter(self, counter_type: str) -> bool:
        """Increment a specific job counter. counter_type: accepted, successful, failed, completed, rejected"""
        try:
            with self.session_scope() as session:
                # Get the singleton job_counts record
                job_counts = session.query(JobCounts).first()
                if not job_counts:
                    # Create if it doesn't exist (fallback)
                    job_counts = JobCounts(accepted=0, successful=0, failed=0, completed=0, rejected=0)
                    session.add(job_counts)
                    session.flush()  # Ensure it's created before updating

                # Increment the specific counter
                if counter_type == "accepted":
                    job_counts.accepted += 1
                elif counter_type == "successful":
                    job_counts.successful += 1
                elif counter_type == "failed":
                    job_counts.failed += 1
                elif counter_type == "completed":
                    job_counts.completed += 1
                elif counter_type == "rejected":
                    job_counts.rejected += 1
                else:
                    logger.warning(f"Unknown counter type: {counter_type}")
                    return False

                job_counts.updated_at = datetime.utcnow()
                logger.debug(f"Incremented {counter_type} counter")
                return True
        except Exception as e:
            logger.error(f"Failed to increment {counter_type} counter: {str(e)}")
            return False

    def get_job_counts(self) -> dict:
        """Get current job counter values"""
        try:
            with self.read_only_session() as session:
                job_counts = session.query(JobCounts).first()
                if job_counts:
                    return {
                        "accepted": job_counts.accepted,
                        "successful": job_counts.successful,
                        "failed": job_counts.failed,
                        "completed": job_counts.completed,
                        "rejected": job_counts.rejected,
                        "updated_at": job_counts.updated_at
                    }
                else:
                    return {"accepted": 0, "successful": 0, "failed": 0, "completed": 0, "rejected": 0, "updated_at": None}
        except Exception as e:
            logger.error(f"Failed to get job counts: {str(e)}")
            return {"accepted": 0, "successful": 0, "failed": 0, "completed": 0, "rejected": 0, "updated_at": None}


db_manager = DatabaseManager()


def get_db() -> Generator[Session, None, None]:
    with db_manager.session_scope() as session:
        yield session


def get_read_only_db() -> Generator[Session, None, None]:
    with db_manager.read_only_session() as session:
        yield session


class BaseRepository:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    @contextmanager
    def get_session(self, auto_commit: bool = True) -> Generator[Session, None, None]:
        with self.db_manager.session_scope(auto_commit=auto_commit) as session:
            yield session

    @contextmanager
    def get_read_session(self) -> Generator[Session, None, None]:
        with self.db_manager.read_only_session() as session:
            yield session
