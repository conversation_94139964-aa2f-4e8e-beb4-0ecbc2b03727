from fastapi import APIRouter, HTTPException
from datetime import datetime
import sys
from typing import Optional
from fastapi import Header
from app.config.settings import config
from app.celery_app import get_celery_broker_url
from app.config.logger_factory import get_logger
from app.helper import get_app_version
from celery import Celery
from app.job_tracking import job_tracker
from app.models import JobHistoryResponse, JobCountersResponse, JobListResponse, QueueStatsResponse
from app.request_validation import check_auth_status
from app.database import db_manager
from app.services.model_preloader import model_preloader
router = APIRouter(prefix="", tags=["stat"])
logger = get_logger(__name__)


@router.get("/workers")
def workers():
    try:
        broker_url = get_celery_broker_url()
        app = Celery('inspect_workers', broker=broker_url)
        i = app.control.inspect()
        workers_status = i.stats() or {}
        workers_len = len(workers_status.items())
        if workers_status:
            return {"workers": workers_status, "workers_len": workers_len, "workers_status": "success"}
        else:
            return {"workers": workers_status, "workers_len": workers_len, "workers_status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/counters", response_model=JobCountersResponse)
async def get_job_counters(authorization: Optional[str] = Header(None)):
    try:
        auth_enabled = config.get("auth.enabled", False)
        if auth_enabled:
            auth_status, auth_response = check_auth_status(authorization, "get_job_counters")
            if auth_status is False:
                return auth_response

        counters = db_manager.get_job_counts()
        # Convert datetime to string if present
        updated_at_str = None
        if counters.get("updated_at") and hasattr(counters["updated_at"], "isoformat"):
            updated_at_str = counters["updated_at"].isoformat()

        return JobCountersResponse(
            accepted=counters["accepted"],
            successful=counters["successful"],
            failed=counters["failed"],
            completed=counters["completed"],
            rejected=counters["rejected"],
            updated_at=updated_at_str
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/rabbitmq/queued")
def jobs_rabbitmq_count():
    try:
        import requests
        queue_name = "video_processing"
        rabbitmq_username = config.get("rabbitmq.username", "rabbitusr")
        rabbitmq_password = config.get("rabbitmq.password", "1lqgEJU3VPyhg")
        rabbitmq_host = config.get("rabbitmq.host", "rabbitmq")
        rabbitmq_api_port = config.get("rabbitmq.api_port", 15672)
        rabbit_api_url = "http://" + rabbitmq_host + ":" + str(rabbitmq_api_port) + "/api/queues/%2f/" + queue_name

        r = requests.get(rabbit_api_url, auth=(rabbitmq_username, rabbitmq_password))
        if r.status_code != 200:
            raise HTTPException(status_code=r.status_code, detail=r.text)

        data = r.json()
        rbt = str(data.get("messages_ready", 0))
        if rbt is None:
            rbt = str(0)

        return {
            "rabbitapi": rbt or {}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/count/{jobtype}")
def jobs_count(jobtype: str):
    try:
        if jobtype == "queued":
            queued_count = job_tracker.get_job_count_by_queue()
            return QueueStatsResponse(count=queued_count)
        elif jobtype == "accepted":
            accepted_result = job_tracker.get_job_count_by_state("accepted")
            return QueueStatsResponse(count=accepted_result["accepted"])
        elif jobtype == "successful":
            successful_result = job_tracker.get_job_count_by_state("successful")
            return QueueStatsResponse(count=successful_result["successful"])
        elif jobtype == "failed":
            failed_result = job_tracker.get_job_count_by_state("failed")
            return QueueStatsResponse(count=failed_result["failed"])
        elif jobtype == "completed":
            completed_result = job_tracker.get_job_count_by_state("completed")
            return QueueStatsResponse(count=completed_result["completed"])
        elif jobtype == "rejected":
            rejected_result = job_tracker.get_job_count_by_state("rejected")
            return QueueStatsResponse(count=rejected_result["rejected"])
        else:
            raise HTTPException(status_code=400, detail="Invalid job type")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/list/{jobtype}")
def jobs_list(jobtype: str):
    try:
        if jobtype == "queued":
            queued_jobs = job_tracker.get_jobs_by_state("queued")
            for job in queued_jobs:
                if "created_at" in job and hasattr(job["created_at"], "isoformat"):
                    job["created_at"] = job["created_at"].isoformat()
            return JobListResponse(jobs=queued_jobs)
        elif jobtype == "finished":
            finished_jobs = job_tracker.get_jobs_by_state("finished")
            for job in finished_jobs:
                if "created_at" in job and hasattr(job["created_at"], "isoformat"):
                    job["created_at"] = job["created_at"].isoformat()
            return JobListResponse(jobs=finished_jobs)
        elif jobtype == "failed":
            failed_jobs = job_tracker.get_jobs_by_state("finished-failed")
            for job in failed_jobs:
                if "created_at" in job and hasattr(job["created_at"], "isoformat"):
                    job["created_at"] = job["created_at"].isoformat()
            return JobListResponse(jobs=failed_jobs)
        else:
            raise HTTPException(status_code=400, detail="Invalid job type")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/job/status/{job_uuid}")
def job_status(job_uuid: str):
    try:
        job = job_tracker.get_job(job_uuid)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Convert datetime objects to strings for JSON serialization
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
        if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
            job["updated_at"] = job["updated_at"].isoformat()
        if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
            job["expires_at"] = job["expires_at"].isoformat()

        return JobHistoryResponse(job=job)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ////////////////////////////////////////////////////////////
# ////////////////////////////////////////////////////////////


@router.get("/health")
async def health_check():
    now = datetime.now()
    dt_string = now.strftime("%Y-%m-%d %H:%M:%S")
    return {"status": dt_string}


@router.get("/health/detail")
def health_detail():
    try:
        broker_url = get_celery_broker_url()
        app = Celery('inspect_workers', broker=broker_url)
        i = app.control.inspect()
        workers_status = i.stats() or {}
        workers_len = len(workers_status.items())
        if workers_status:
            return {"api_status": "success", "workers_len": workers_len, "workers_status": workers_status}
        else:
            return {"api_status": "success", "workers_len": workers_len, "workers_status": workers_status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/version")
async def appversion():
    try:
        import torch
        python_version = sys.version
        torch_version = torch.__version__
        cuda_version = torch.version.cuda
        cudnn_version = torch.backends.cudnn.version()

        return {"cloudml_version": get_app_version(), "python_version": python_version, "torch_version": torch_version, "cudnn_version": cudnn_version, "cuda_version": cuda_version}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))





@router.get("/")
async def root():
    return {"status": "success"}
