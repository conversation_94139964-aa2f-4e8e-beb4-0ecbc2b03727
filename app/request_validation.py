import datetime
import json
import os
import secrets
import time
from datetime import timezone

import jwt
from fastapi.responses import JSONResponse
from jwt.exceptions import DecodeError, ExpiredSignatureError, InvalidSignatureError, InvalidTokenError

from app.database import db_manager
from app.config.logger_factory import get_logger
from app.config.settings import config
from app.helper import find_existing
from app.job_tracking import job_tracker
from app.models import DetectionRequest, DetectionResponse, TaskParameters

logger = get_logger(__name__)


def generate_traceid():
    import secrets

    trace_id = secrets.token_hex(16).upper()
    return str(trace_id)


def generate_spanid():
    import secrets

    span_id = secrets.token_hex(8).upper()
    return str(span_id)


def check_auth_token():
    auth_enabled = config.get("auth.enabled", False)
    return auth_enabled


def check_auth_status(authorization, job_uuid):
    try:
        if not authorization:
            retval = DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="authorization header missing",
            )
            return False, JSONResponse(content=retval.model_dump())

        if not authorization.startswith("Bearer "):
            retval = DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="invalid authorization header",
            )
            return False, JSONResponse(content=retval.model_dump())

        token = authorization.split(" ")[1]
        verify_auth_token(token)
        return True, None
    except jwt.ExpiredSignatureError:
        retval = DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Token has expired",
        )
        return False, JSONResponse(content=retval.model_dump())
    except jwt.InvalidSignatureError:
        retval = DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token signature",
        )
        return False, JSONResponse(content=retval.model_dump())
    except jwt.DecodeError:
        retval = DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token format",
        )
        return False, JSONResponse(content=retval.model_dump())
    except jwt.InvalidTokenError:
        retval = DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token",
        )
        return False, JSONResponse(content=retval.model_dump())


def verify_auth_token(token: str):
    try:
        jwt_secret = config.get("auth.secret_key", "")
        if not jwt_secret:
            raise jwt.InvalidTokenError("JWT secret key is not configured")

        jwt_algorithm = config.get("auth.algorithm", "HS256")
        allowed_algorithms = ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]
        if jwt_algorithm not in allowed_algorithms:
            raise jwt.InvalidTokenError(f"Unsupported JWT algorithm: {jwt_algorithm}")

        # Token format validation
        parts = token.split(".")
        if len(parts) != 3:
            raise jwt.DecodeError("Invalid token format")

        # Decode and verify token
        decoded_token = jwt.decode(
            token,
            jwt_secret,
            algorithms=[jwt_algorithm],
            options={
                "verify_signature": True,
                "verify_exp": True,
                "verify_iat": True,
                "verify_nbf": True,
                "require": ["exp", "iat", "sub", "iss"],
            },
            leeway=30,
        )

        # Validate required claims
        required_claims = ["sub", "iss", "iat", "exp"]
        for claim in required_claims:
            if claim not in decoded_token:
                raise jwt.InvalidTokenError(f"Missing required claim: {claim}")

        # Validate issuer
        allowed_issuers = config.get("auth.allowed_issuers", ["ezlo-auth-service"])
        if decoded_token["iss"] not in allowed_issuers:
            raise jwt.InvalidTokenError(f"Invalid token issuer: {decoded_token['iss']}")

        # Validate timestamps
        iat = decoded_token["iat"]
        exp = decoded_token["exp"]
        nbf = decoded_token.get("nbf", iat)  # Not Before Time

        current_time = int(time.time())
        if current_time < nbf:
            raise jwt.InvalidTokenError("Token is not yet valid")

        if isinstance(iat, (int, float)):
            iat = datetime.datetime.fromtimestamp(iat, tz=timezone.utc)
        if isinstance(exp, (int, float)):
            exp = datetime.datetime.fromtimestamp(exp, tz=timezone.utc)

        # Validate token duration
        token_duration = exp - iat
        max_duration = datetime.timedelta(hours=24)
        min_duration = datetime.timedelta(minutes=5)

        if token_duration > max_duration:
            logger.error(f"Token validity ({token_duration}) exceeds maximum allowed ({max_duration})")
            raise jwt.ExpiredSignatureError(f"Token validity ({token_duration}) exceeds maximum allowed ({max_duration})")

        if token_duration < min_duration:
            logger.error(f"Token validity ({token_duration}) is less than minimum required ({min_duration})")
            raise jwt.InvalidTokenError(f"Token validity ({token_duration}) is less than minimum required ({min_duration})")

        if datetime.datetime.now(timezone.utc) > exp:
            raise jwt.ExpiredSignatureError("Token has expired")

        return decoded_token

    except ExpiredSignatureError:
        logger.warning("JWT token has expired")
        raise
    except InvalidSignatureError:
        logger.warning("Invalid JWT signature detected")
        raise
    except DecodeError as e:
        logger.warning(f"JWT decode error: {str(e)}")
        raise
    except InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during JWT validation: {str(e)}")
        raise jwt.InvalidTokenError("Token validation failed")


def update_detection_classes(items, classes):
    response_items = None
    if items and isinstance(items, list):
        if len(items) > 0:
            result_items = find_existing(classes, items)
            if len(result_items) == 0:
                response_items = None
            else:
                response_items = result_items
        else:
            response_items = None
    else:
        response_items = None

    return response_items


def check_filtering_items(detection_config, available_classes):
    if detection_config.enabled:
        if detection_config.items is None:
            detection_config.items = available_classes
        else:
            object_new_items = update_detection_classes(detection_config.items, available_classes)
            if object_new_items is None:
                detection_config.enabled = False
                detection_config.items = None
            else:
                detection_config.items = object_new_items
    else:
        detection_config.items = None


# This method both checks and enriches the incoming json payload values
def validate_request(request: DetectionRequest, authorization) -> JSONResponse | None:
    try:
        task_params = TaskParameters(**request.model_dump())
        try:
            # Extract UUID from file info
            job_uuid = task_params.file.uuid
        except Exception as e:
            logger.error(f"There is no uuid in request: {e}")
            retval = DetectionResponse(
                status="failed",
                status_code=500,
                task_id="",
                uuid="",
                message="there is no uuid in request",
            )
            return JSONResponse(content=retval.model_dump())

        # Check if job with UUID already exists
        if job_tracker.job_exists(job_uuid):
            logger.warning(f"Job with UUID {job_uuid} already exists")
            db_manager.increment_job_counter("rejected")
            retval = DetectionResponse(
                status="failed",
                status_code=409,
                task_id="",
                uuid=job_uuid,
                message="job already exists",
            )
            return JSONResponse(content=retval.model_dump())

        if task_params.ot is None or task_params.ot.trace_id is None:
            task_params.ot.trace_id = str(secrets.token_hex(16).upper())
            task_params.ot.span_id = str(secrets.token_hex(8).upper())

        # Check authentication if enabled
        auth_enabled = config.get("auth.enabled", False)
        if auth_enabled:
            auth_status, auth_response = check_auth_status(authorization, job_uuid)
            if auth_status is False:
                return auth_response

        # File Meta Information
        rqfile_meta_controller_uuid = ""
        rqfile_meta_device_id = ""
        ReqFile = task_params.file
        if ReqFile is not None:
            rqfile_meta = ReqFile.meta
            if rqfile_meta is None or rqfile_meta == "":
                logger.debug(f"payload file meta is not json format uuid:{job_uuid}")
            else:
                try:
                    rqfile_meta_json = json.loads(rqfile_meta)
                    rqfile_meta_controller_info = rqfile_meta_json.get("controller_info", {})
                    rqfile_meta_controller_uuid = rqfile_meta_controller_info.get("controller_uuid", "")
                    rqfile_meta_device_id = rqfile_meta_controller_info.get("device_id", "")
                except json.JSONDecodeError as e:
                    logger.debug(f"payload meta is not json format uuid:{job_uuid} err:{e}")

        task_params.hostname = os.uname().nodename
        task_params.controller_uuid = rqfile_meta_controller_uuid
        task_params.device_id = rqfile_meta_device_id
        task_params.object_detection_device = config.get("object_detection.device", "gpu")

        task_params.model_standard_path = config.get("standard.model", "app/ezlomodels/ezlo-yolo-standard-11s.pt")
        task_params.model_package_path = config.get("package.model", "app/ezlomodels/ezlo-yolowsv2.pt")
        task_params.model_custom_package_path = config.get("custom_package.model", "app/ezlomodels/ezlo-custom-package.pt")
        task_params.model_license_plate_path = config.get(
            "license_plate_recognition.model",
            "app/ezlomodels/license_plate_detector.pt",
        )

        task_params.facerec_const_tolerance = config.get("face_recognition.const_tolerance", 0.71)

        task_params.license_plate_recognition_country = config.get("license_plate_recognition.country", "us")

        task_params.person_detection_classes = config.get("object_detection.person_detection_classes", ["person"])
        task_params.animal_detection_classes = config.get("object_detection.animal_detection_classes", ["cat", "dog"])
        task_params.vehicle_detection_classes = config.get("object_detection.vehicle_detection_classes", ["car", "motorcycle", "truck"])
        task_params.package_detection_classes = config.get(
            "object_detection.package_detection_classes",
            [
                "parcel",
                "box",
                "bundle",
                "packet",
                "package",
                "crate",
                "carton",
                "envelope",
                "case",
                "kit",
                "bag",
            ],
        )

        if task_params.db.configuration.global_threshold is None:
            task_params.db.configuration.global_threshold = config.get("object_detection.global_confidence_threshold", 0.65)
        task_params.db.configuration.global_threshold = float(task_params.db.configuration.global_threshold)

        if task_params.db.configuration.standard_skip_frame is None:
            task_params.db.configuration.standard_skip_frame = config.get("standard.skip_frame_ratio", 0.5)
        task_params.db.configuration.standard_skip_frame = float(task_params.db.configuration.standard_skip_frame)

        if task_params.db.configuration.package_skip_frame is None:
            task_params.db.configuration.package_skip_frame = config.get("package.skip_frame_ratio", 0.4)
        task_params.db.configuration.package_skip_frame = float(task_params.db.configuration.package_skip_frame)

        check_filtering_items(
            task_params.db.configuration.animal_detection,
            task_params.animal_detection_classes,
        )
        check_filtering_items(
            task_params.db.configuration.vehicle_detection,
            task_params.vehicle_detection_classes,
        )

        task_params.ocr_server_url = config.get("ocr_server.url", "http://ocrserver:8282/base64")
        task_params.openalpr_server_url = config.get("openalpr_server.url", "http://openalpr:8181/alpr")

        person_detection = task_params.db.configuration.person_detection.enabled
        face_recognition = task_params.db.configuration.face_recognition.enabled
        animal_detection = task_params.db.configuration.animal_detection.enabled
        vehicle_detection = task_params.db.configuration.vehicle_detection.enabled
        package_detection = task_params.db.configuration.package_detection.enabled
        plate_detection = task_params.db.configuration.plate_detection.enabled
        barcode_detection = task_params.db.configuration.barcode_detection.enabled

        hotzones = task_params.db.configuration.hotzones.zones
        if len(hotzones) > 0:
            for hzone in hotzones:
                hzone.points = [p for p in hzone.points if p.x is not None and p.y is not None]

        odetections = [
            person_detection,
            face_recognition,
            animal_detection,
            vehicle_detection,
            package_detection,
            plate_detection,
            barcode_detection,
        ]

        if not any(odetections):
            retval = DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="one of the objects person, face recognition, vehicle, animal or package must be defined as true",
            )
            db_manager.increment_job_counter("rejected")
            return JSONResponse(content=retval.model_dump())

        return JSONResponse(content=task_params.model_dump())
    except Exception as err:
        import sys

        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        respmes = f"request_validation Exception with error: {err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        logger.error(respmes)
