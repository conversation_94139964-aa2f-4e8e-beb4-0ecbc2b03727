import os
import sys
import time

import torch

import app.helper as helper
from app.config.logger_factory import get_logger, set_logging_context
from app.helper import (get_package_class, get_package_confidence_threshold,
                        get_skip_frames, get_standard_class_index,
                        hotzone_video_info, prepare_detection_result)
from app.job_tracking import job_tracker
from app.models import TaskParameters
from app.services.model_manager import model_manager
from app.step32_standard_detection import step32_standard_detection_run
from app.step33_package_detection_custom import \
    step33_package_detection_custom_run
from app.step33_package_detection_diff_frame import \
    step33_package_detection_diff_frame_run
from app.step33_package_detection_yoloworld import \
    step33_package_detection_yoloworld_run
from app.step34_face_recognition import step34_face_recognition_run
from app.step35_licence_plate import step35_licence_plate_run
from app.step36_barcode import step36_barcode_run

logger = get_logger(__name__)


def step31_objectDetection_run(job_uuid: str, task_params_dict: dict, detection_file: str):
    try:
        task_params = TaskParameters(**task_params_dict)

        # Set logging context for this job
        ot_trace_id = task_params.ot.trace_id
        ot_span_id = task_params.ot.span_id
        set_logging_context(job_uuid=job_uuid, trace_id=ot_trace_id, span_id=ot_span_id)

        response_callback = task_params.response.callback
        confidence_threshold = task_params.db.configuration.global_threshold
        cuda_is_avaliable = torch.cuda.is_available()

        device = task_params.object_detection_device
        if device == "cpu":
            device = "cpu"
        elif device == "gpu":
            if cuda_is_avaliable:
                device = "cuda"
            else:
                device = "cpu"
        else:
            device = "cpu"

        controller_uuid = task_params.controller_uuid
        device_id = task_params.device_id
        hostname = task_params.hostname

        logger.info(f"ml_device:{device} cuda_is_avaliable:{cuda_is_avaliable} hostname:{hostname}")
        hotzone_detection = task_params.db.configuration.hotzones.enabled
        hotzones = task_params.db.configuration.hotzones.zones
        standard_skip_frame = task_params.db.configuration.standard_skip_frame
        package_skip_frame = task_params.db.configuration.package_skip_frame

        hotzone_zones_polygon, video_fps, video_width, video_height, video_fps = hotzone_video_info(job_uuid, detection_file, hotzone_detection, hotzones)

        # STANDARD - Using lazy loading
        if task_params.db.configuration.person_detection.enabled or task_params.db.configuration.animal_detection.enabled or task_params.db.configuration.vehicle_detection.enabled:
            startPersonAnimalVehicleTimeUnixNano = time.time_ns()
            standard_skip_frames = get_skip_frames(video_fps, standard_skip_frame)
            standard_confidence_threshold = confidence_threshold

            # Lazy load standard model with caching
            standard_model = model_manager.get_standard_model(model_path=task_params.model_standard_path, device=device)

            standard_class_index = get_standard_class_index(
                standard_model,
                task_params.db.configuration.person_detection.enabled,
                task_params.person_detection_classes,
                task_params.db.configuration.vehicle_detection.enabled,
                task_params.vehicle_detection_classes,
                task_params.db.configuration.animal_detection.enabled,
                task_params.animal_detection_classes,
            )

            logger.info(f"person_animal_vehicle_detection standard_confidence_threshold:{standard_confidence_threshold} model_file:{task_params.model_standard_path} standard_skip_frame_factor:{standard_skip_frame} standard_skip_frames:{standard_skip_frames} detection_file:{detection_file} detection_file_isexist:{os.path.isfile(detection_file)} hotzone_enabled:{hotzone_detection} hotzone_zones_polygon:{hotzone_zones_polygon}")
            step32_standard_detection_run(
                job_uuid,
                standard_model,
                detection_file,
                hotzone_zones_polygon,
                standard_class_index,
                standard_confidence_threshold,
                standard_skip_frames,
                device,
                task_params.person_detection_classes,
                task_params.vehicle_detection_classes,
                task_params.animal_detection_classes,
            )

            helper.send_open_telemetry(
                job_uuid,
                ot_trace_id,
                ot_span_id,
                1,
                "",
                "worker",
                ["function"],
                ["object_detection"],
                "person_animal_vehicle_detection",
                [
                    "jobid",
                    "controller_uuid",
                    "device_id",
                    "person_detection",
                    "animal_detection",
                    "vehicle_detection",
                    "threshold",
                    "device",
                    "hotzone_detection",
                ],
                [
                    job_uuid,
                    controller_uuid,
                    device_id,
                    task_params.db.configuration.person_detection.enabled,
                    task_params.db.configuration.animal_detection.enabled,
                    task_params.db.configuration.vehicle_detection.enabled,
                    standard_confidence_threshold,
                    device,
                    hotzone_detection,
                ],
                startPersonAnimalVehicleTimeUnixNano,
            )

        # PACKAGE
        if task_params.db.configuration.package_detection.enabled:
            package_skip_frames = get_skip_frames(video_fps, package_skip_frame)
            package_confidence_threshold = get_package_confidence_threshold(confidence_threshold)

            package_total_found_classes = {}
            package_detected = False

            # Lazy load custom package model with caching
            custom_package_model = model_manager.get_custom_package_model(model_path=task_params.model_custom_package_path, device=device)

            # CUSTOM MODEL
            if not package_detected:
                startPackageCustomTimeUnixNano = time.time_ns()

                logger.info(f"package_custom_detection package_confidence_threshold:{package_confidence_threshold} model_file:{task_params.model_custom_package_path} package_skip_frame_factor:{package_skip_frame} package_skip_frames:{package_skip_frames} detection_file:{detection_file} detection_file_isexist:{os.path.isfile(detection_file)}")
                step33_package_detection_custom_results = step33_package_detection_custom_run(
                    job_uuid,
                    custom_package_model,
                    detection_file,
                    hotzone_zones_polygon,
                    package_confidence_threshold,
                    package_skip_frames,
                    device,
                    response_callback,
                )
                package_total_found_classes = helper.get_total_results(package_total_found_classes, step33_package_detection_custom_results)
                if len(package_total_found_classes) > 0:
                    package_detected = True

                helper.send_open_telemetry(
                    job_uuid,
                    ot_trace_id,
                    ot_span_id,
                    1,
                    "",
                    "worker",
                    ["function"],
                    ["object_detection"],
                    "package_custom_detection",
                    [
                        "jobid",
                        "controller_uuid",
                        "device_id",
                        "threshold",
                        "device",
                        "hotzone_detection",
                    ],
                    [
                        job_uuid,
                        controller_uuid,
                        device_id,
                        package_confidence_threshold,
                        device,
                        hotzone_detection,
                    ],
                    startPackageCustomTimeUnixNano,
                )

            # DIFF FRAME
            if not package_detected:
                startPackageDiffFrameTimeUnixNano = time.time_ns()

                logger.info(f"package_diff_frame_detection package_confidence_threshold:{package_confidence_threshold} model_file:{task_params.model_custom_package_path} detection_file:{detection_file} detection_file_isexist:{os.path.isfile(detection_file)}")
                step33_package_detection_diff_frame_results = step33_package_detection_diff_frame_run(
                    job_uuid,
                    custom_package_model,
                    detection_file,
                    hotzone_zones_polygon,
                    package_confidence_threshold,
                    device,
                )
                package_total_found_classes = helper.get_total_results(
                    package_total_found_classes,
                    step33_package_detection_diff_frame_results,
                )
                if len(package_total_found_classes) > 0:
                    package_detected = True

                helper.send_open_telemetry(
                    job_uuid,
                    ot_trace_id,
                    ot_span_id,
                    1,
                    "",
                    "worker",
                    ["function"],
                    ["object_detection"],
                    "package_diff_frame_detection",
                    [
                        "jobid",
                        "controller_uuid",
                        "device_id",
                        "threshold",
                        "device",
                        "hotzone_detection",
                    ],
                    [
                        job_uuid,
                        controller_uuid,
                        device_id,
                        package_confidence_threshold,
                        device,
                        hotzone_detection,
                    ],
                    startPackageDiffFrameTimeUnixNano,
                )

            # YOLOWORLD
            if not package_detected:
                startPackageYoloWorldTimeUnixNano = time.time_ns()

                # Lazy load package model with caching
                package_model = model_manager.get_package_model(model_path=task_params.model_package_path, device=device)

                mclasses = get_package_class(task_params.package_detection_classes)
                package_model.set_classes(mclasses)

                logger.info(f"package_yoloworld_detection package_confidence_threshold:{package_confidence_threshold} model_file:{task_params.model_package_path} package_skip_frames:{package_skip_frames} detection_file:{detection_file} detection_file_isexist:{os.path.isfile(detection_file)}")
                step33_package_detection_yoloworld_results = step33_package_detection_yoloworld_run(
                    job_uuid,
                    package_model,
                    detection_file,
                    hotzone_zones_polygon,
                    package_confidence_threshold,
                    package_skip_frames,
                    device,
                    response_callback,
                )
                package_total_found_classes = helper.get_total_results(
                    package_total_found_classes,
                    step33_package_detection_yoloworld_results,
                )
                if len(package_total_found_classes) > 0:
                    package_detected = True

                helper.send_open_telemetry(
                    job_uuid,
                    ot_trace_id,
                    ot_span_id,
                    1,
                    "",
                    "worker",
                    ["function"],
                    ["object_detection"],
                    "package_yoloworld_detection",
                    [
                        "jobid",
                        "controller_uuid",
                        "device_id",
                        "threshold",
                        "device",
                        "hotzone_detection",
                    ],
                    [
                        job_uuid,
                        controller_uuid,
                        device_id,
                        package_confidence_threshold,
                        device,
                        hotzone_detection,
                    ],
                    startPackageYoloWorldTimeUnixNano,
                )

            if len(package_total_found_classes) > 0:
                prepare_detection_result(job_uuid, "package", "package")

        # FACE RECOGNITION
        if task_params.db.configuration.face_recognition.enabled:
            startFaceRecTimeUnixNano = time.time_ns()
            facerec_confidence_threshold = confidence_threshold
            facerec_skip_frames = get_skip_frames(video_fps, standard_skip_frame)

            logger.info(f"face_recognition facerec_confidence_threshold:{facerec_confidence_threshold} facerec_const_tolerance:{task_params.facerec_const_tolerance}")
            step34_face_recognition_run(
                job_uuid,
                detection_file,
                task_params.db.elements.known_people,
                task_params.db.configuration.face_recognition.items,
                confidence_threshold,
                task_params.facerec_const_tolerance,
                facerec_skip_frames,
            )

            helper.send_open_telemetry(
                job_uuid,
                ot_trace_id,
                ot_span_id,
                1,
                "",
                "worker",
                ["function"],
                ["object_detection"],
                "face_recognition_detection",
                [
                    "jobid",
                    "controller_uuid",
                    "device_id",
                    "threshold",
                    "device",
                    "hotzone_detection",
                ],
                [
                    job_uuid,
                    controller_uuid,
                    device_id,
                    facerec_confidence_threshold,
                    device,
                    hotzone_detection,
                ],
                startFaceRecTimeUnixNano,
            )

        # LICENCE PLATE
        if task_params.db.configuration.plate_detection.enabled:
            startLicencePlateTimeUnixNano = time.time_ns()
            plate_detection_country = task_params.license_plate_recognition_country
            lplate_confidence_threshold = confidence_threshold
            lplate_skip_frames = get_skip_frames(video_fps, package_skip_frame)
            ocr_server_url = task_params.ocr_server_url
            openalpr_server_url = task_params.openalpr_server_url

            # Lazy load license plate model with caching
            lplate_model = model_manager.get_license_plate_model(model_path=task_params.model_license_plate_path, device=device)

            logger.info(f"license_plate_recognition lplate_confidence_threshold:{lplate_confidence_threshold} model_file:{task_params.model_license_plate_path} lplate_skip_frames:{lplate_skip_frames}")
            step35_licence_plate_run(
                job_uuid,
                lplate_model,
                detection_file,
                task_params.db.configuration.plate_detection.items,
                hotzone_zones_polygon,
                lplate_confidence_threshold,
                device,
                lplate_skip_frames,
                plate_detection_country,
                ocr_server_url,
                openalpr_server_url,
            )

            helper.send_open_telemetry(
                job_uuid,
                ot_trace_id,
                ot_span_id,
                1,
                "",
                "worker",
                ["function"],
                ["object_detection"],
                "licence_plate_detection",
                [
                    "jobid",
                    "controller_uuid",
                    "device_id",
                    "threshold",
                    "device",
                    "hotzone_detection",
                ],
                [
                    job_uuid,
                    controller_uuid,
                    device_id,
                    lplate_confidence_threshold,
                    device,
                    hotzone_detection,
                ],
                startLicencePlateTimeUnixNano,
            )

        # BARCODE
        if task_params.db.configuration.barcode_detection.enabled:
            startBarcodeTimeUnixNano = time.time_ns()

            logger.info(f"barcode_recognition items:{task_params.db.configuration.barcode_detection.items}")
            step36_barcode_run(
                job_uuid,
                detection_file,
                task_params.db.configuration.barcode_detection.items,
            )

            helper.send_open_telemetry(
                job_uuid,
                ot_trace_id,
                ot_span_id,
                1,
                "",
                "worker",
                ["function"],
                ["object_detection"],
                "barcode_detection",
                [
                    "jobid",
                    "controller_uuid",
                    "device_id",
                    "device",
                    "hotzone_detection",
                ],
                [job_uuid, controller_uuid, device_id, device, hotzone_detection],
                startBarcodeTimeUnixNano,
            )

    except Exception as err:
        job_tracker.update_job_state(job_uuid, "objectdetection", "failed")
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step31_objectDetection_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
