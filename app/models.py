from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field


# OpenTelemetry models
class OpenTelemetryInfo(BaseModel):
    trace_id: Optional[str] = None
    span_id: Optional[str] = None


# Resource models
class ResourceInfo(BaseModel):
    method: str
    url: str


# File models
class FileInfo(BaseModel):
    uuid: str
    timestamp: Optional[int] = None
    size: Optional[int] = None
    filename: Optional[str] = None
    file_format: Optional[str] = None
    meta: Optional[str] = None


class FileReference(BaseModel):
    uuid: str
    meta: Dict[str, Any]


class KnownPerson(BaseModel):
    uuid: str
    name: str
    meta: Dict[str, Any]
    files: List[FileReference]


class DatabaseElements(BaseModel):
    known_people: Optional[List[KnownPerson]] = None


# Detection configuration models
class PersonDetectionConfig(BaseModel):
    enabled: Optional[bool] = False


class PackageDetectionConfig(BaseModel):
    enabled: Optional[bool] = False


class AnimalDetectionConfig(BaseModel):
    enabled: Optional[bool] = False
    items: Optional[List[str]] = None


class FaceRecognitionConfig(BaseModel):
    enabled: Optional[bool] = False
    items: Optional[List[str]] = None


class BarcodeDetectionConfig(BaseModel):
    enabled: Optional[bool] = False
    items: Optional[List[str]] = None


class VehicleDetectionConfig(BaseModel):
    enabled: Optional[bool] = False
    items: Optional[List[str]] = None


class PlateDetectionConfig(BaseModel):
    enabled: Optional[bool] = False
    items: Optional[List[str]] = None


class HotZonePoint(BaseModel):
    x: Optional[int] = None
    y: Optional[int] = None


class HotZone(BaseModel):
    id: int
    maxwidth: int
    maxheight: int
    points: List[HotZonePoint]


class HotZonesConfig(BaseModel):
    enabled: bool
    zones: List[HotZone]


class DetectionConfiguration(BaseModel):
    standard_skip_frame: Optional[Union[float, str]] = None
    package_skip_frame: Optional[Union[float, str]] = None
    global_threshold: Optional[Union[float, str]] = None
    person_detection: Optional[PersonDetectionConfig] = PersonDetectionConfig(enabled=False)
    package_detection: Optional[PackageDetectionConfig] = PackageDetectionConfig(enabled=False)
    animal_detection: Optional[AnimalDetectionConfig] = AnimalDetectionConfig(enabled=False, items=None)
    face_recognition: Optional[FaceRecognitionConfig] = FaceRecognitionConfig(enabled=False, items=None)
    barcode_detection: Optional[BarcodeDetectionConfig] = BarcodeDetectionConfig(enabled=False, items=None)
    vehicle_detection: Optional[VehicleDetectionConfig] = VehicleDetectionConfig(enabled=False, items=None)
    plate_detection: Optional[PlateDetectionConfig] = PlateDetectionConfig(enabled=False, items=None)
    hotzones: Optional[HotZonesConfig] = HotZonesConfig(enabled=False, zones=[])


class DatabaseInfo(BaseModel):
    elements: Optional[DatabaseElements] = None
    configuration: DetectionConfiguration
    files: Optional[Dict[str, Any]] = None


class ResponseInfo(BaseModel):
    callback: str


class DetectionRequest(BaseModel):
    ot: Optional[OpenTelemetryInfo] = OpenTelemetryInfo(trace_id=None, span_id=None)
    resources: ResourceInfo
    file: FileInfo
    db: DatabaseInfo
    response: ResponseInfo


class DetectionRequestHeaders(BaseModel):
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None


class DetectionResponse(BaseModel):
    status: str
    status_code: int
    task_id: str
    uuid: str
    message: str


class ErrorResponse(BaseModel):
    status: str
    message: str


class QueueStatsResponse(BaseModel):
    count: int


class DbStatsResponse(BaseModel):
    accepted: int
    successful: int
    failed: int
    completed: int
    rejected: int


class JobListResponse(BaseModel):
    jobs: List[dict]  # Simplified for now


class WorkerInfoResponse(BaseModel):
    workers: List[dict]  # Simplified for now


class JobHistoryResponse(BaseModel):
    job: dict  # Simplified for now


class JobCountersResponse(BaseModel):
    accepted: int
    successful: int
    failed: int
    completed: int
    rejected: int
    updated_at: Optional[str] = None


class WorkerStatistic(BaseModel):
    worker_id: str
    hostname: str
    state: str
    count: int


class WorkerStatisticsResponse(BaseModel):
    worker_statistics: List[WorkerStatistic]


class JobInfo(BaseModel):
    uuid: str
    state: str
    step: Optional[str] = None
    task_id: Optional[str] = None
    worker_id: Optional[str] = None
    hostname: Optional[str] = None
    created_at: str
    updated_at: str
    expires_at: str
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None


class WorkerJobsResponse(BaseModel):
    jobs: List[JobInfo]
    worker_id: str


class HostnameJobsResponse(BaseModel):
    jobs: List[JobInfo]
    hostname: str


####


class TaskParameters(DetectionRequest):
    object_detection_device: Optional[str] = None

    model_standard_path: Optional[str] = None
    model_package_path: Optional[str] = None
    model_custom_package_path: Optional[str] = None
    model_license_plate_path: Optional[str] = None

    license_plate_recognition_country: Optional[str] = None

    person_detection_classes: Optional[List[str]] = None
    animal_detection_classes: Optional[List[str]] = None
    vehicle_detection_classes: Optional[List[str]] = None
    package_detection_classes: Optional[List[str]] = None

    facerec_const_tolerance: Optional[float] = None

    hostname: Optional[str] = None
    controller_uuid: Optional[str] = None
    device_id: Optional[str] = None

    ocr_server_url: Optional[str] = None
    openalpr_server_url: Optional[str] = None

    video_width: Optional[int] = None
    video_height: Optional[int] = None
    video_fps: Optional[float] = None
    video_frame_count: Optional[int] = None
    video_duration: Optional[float] = None

####


class BBox(BaseModel):
    xmin: int = 0
    ymin: int = 0
    xmax: int = 0
    ymax: int = 0


class DetectionData(BaseModel):
    label: str
    label_element: str
    timestamp: Optional[int] = None
    milisec: Optional[int] = None
    class_: str = Field(None, alias="class")
    conf: int = 0
    bbox: BBox = BBox(xmin=0, ymin=0, xmax=0, ymax=0)
    known_people_uuid: Optional[str] = None

    model_config = ConfigDict(populate_by_name=True, arbitrary_types_allowed=True)


class DetectionType(str, Enum):
    person = "person"
    animal = "animal"
    vehicle = "vehicle"
    knownpeople = "knownpeople"
    barcode = "barcode"
    licenseplate = "licenseplate"


class Detection(BaseModel):
    uuid: str
    type: DetectionType
    data: DetectionData
