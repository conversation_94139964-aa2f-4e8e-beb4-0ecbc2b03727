import os

from app.config.logger_factory import get_logger
from app.utils.logging_utils import StepExecutionContext

logger = get_logger(__name__)


def step20_validate_file_run(job_uuid, detection_file):
    try:
        with StepExecutionContext(job_uuid, "filevalidation", logger):
            if not os.path.isfile(detection_file):
                raise Exception(f"File not found: {detection_file}")

            file_size = os.path.getsize(detection_file)
            if not file_size > 0:
                raise Exception(f"File is empty: {detection_file}")

            file_size_status = True if file_size / (1024 * 1024) >= 1 else False
            if not file_size_status:
                raise Exception(f"File is too small: {detection_file} size: {file_size}")

            if not detection_file.endswith(".mp4"):
                raise Exception(f"File is not a video: {detection_file}")

        logger.debug(f"Video validated successfully: {detection_file}")
    except Exception as e:
        raise Exception(f"20|Unexpected error while file validation err:{str(e)}")
