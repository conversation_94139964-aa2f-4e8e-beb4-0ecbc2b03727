import json
from contextlib import asynccontextmanager
from typing import Optional


from fastapi import <PERSON><PERSON><PERSON>, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.celery_app import process_video
from app.config.logger_factory import get_logger, set_logging_context
from app.database import db_manager
from app.job_tracking import job_tracker
from app.models import DetectionRequest, DetectionResponse, ErrorResponse, TaskParameters
from app.request_validation import validate_request
from app.routes.stat import router as stat_router
from app.services.model_preloader import shutdown_event, startup_event


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await startup_event()
    yield
    # Shutdown
    await shutdown_event()


app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting person, animals, vehicles, packages, faces, license plates, and barcodes in videos",
    version="1.0.0",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(stat_router)


logger = get_logger(__name__)


@app.post(
    "/object_detection",
    response_model=DetectionResponse,
    responses={409: {"model": ErrorResponse}},
)
async def detect_objects(request: DetectionRequest, authorization: Optional[str] = Header(None)):
    response: JSONResponse = validate_request(request, authorization)
    content_bytes = response.body
    content_dict = json.loads(content_bytes)
    if "status_code" in content_dict:
        return response

    task_params = TaskParameters(**content_dict)
    job_uuid = task_params.file.uuid

    try:
        # Create the Celery task first to get the task_id
        task = process_video.delay(job_uuid=job_uuid, task_params_dict=task_params.model_dump())
        task_id = task.id

        # Now create the job with the task_id
        job_tracker.create_job(job_uuid, task_params.ot.trace_id, task_params.ot.span_id, task_id)

        # Set logging context for this HTTP request
        set_logging_context(job_uuid=job_uuid, trace_id=task_params.ot.trace_id, task_id=task_id)

        job_tracker.update_job_state(job_uuid, "process", "queued", None, task_id)

        return DetectionResponse(
            status="queued",
            status_code=200,
            task_id=task_id,
            uuid=job_uuid,
            message="job added successfully",
        )
    except Exception as e:
        error_msg = f"Failed to queue task: {str(e)}"
        logger.error(error_msg)
        return DetectionResponse(
            status="failed",
            status_code=500,
            task_id="",
            uuid="",
            message=error_msg,
        )


@app.post("/postgres_init")
async def postgres_init():
    """Initialize critical PostgreSQL operations like indexes and job counts"""
    try:
        logger.info("PostgreSQL initialization endpoint called")
        result = db_manager.init_critical_operations()

        if result["status"] == "success":
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "message": result["message"]
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": result["message"]
                }
            )
    except Exception as e:
        error_msg = f"Failed to initialize PostgreSQL: {str(e)}"
        logger.error(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": error_msg
            }
        )
