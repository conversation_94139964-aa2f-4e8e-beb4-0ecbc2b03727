import os
import socket

from celery import Celery

from app.config.logger_factory import get_logger, set_logging_context
from app.config.settings import config
from app.job_tracking import job_tracker
from app.models import TaskParameters
from app.step10_download_file import step10_download_file_run
from app.step20_validate_file import step20_validate_file_run
from app.step30_before_detection import step30_before_detection_run
from app.step40_send_result import step40_send_result_run
from app.step50_clean_environment import step50_clean_environment_run
from app.database import db_manager

logger = get_logger(__name__)


def get_celery_broker_url():
    rabbitmq_username = config.get("rabbitmq.username", "rabbitusr")
    rabbitmq_password = config.get("rabbitmq.password", "1lqgEJU3VPyhg")
    rabbitmq_host = config.get("rabbitmq.host", "rabbitmq")
    rabbitmq_port = config.get("rabbitmq.port", 5672)
    rabbitmq_vhost = config.get("rabbitmq.vhost", "/")
    broker_url = f"amqp://{rabbitmq_username}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}{rabbitmq_vhost}"

    return broker_url


def get_celery_result_backend():
    postgresql_username = config.get("postgresql.username", "postgres")
    postgresql_password = config.get("postgresql.password", "4GfW42eVb")
    postgresql_host = config.get("postgresql.host", "postgresql")
    postgresql_port = config.get("postgresql.port", 5432)
    postgresql_database = config.get("postgresql.database", "object_detection")
    result_broker_url = f"db+postgresql://{postgresql_username}:{postgresql_password}@{postgresql_host}:{postgresql_port}/{postgresql_database}"

    return result_broker_url


def make_celery():
    celery_app = Celery("object_detection_worker")

    broker_url = get_celery_broker_url()
    result_broker_url = get_celery_result_backend()

    if broker_url != "" and result_broker_url != "":
        celery_app.conf.update(
            broker_url=broker_url,
            result_backend=result_broker_url,
            task_serializer="json",
            accept_content=["json"],
            result_serializer="json",
            timezone="UTC",
            enable_utc=True,
            task_routes={"app.celery_app.process_video": {"queue": "video_processing"}},
            worker_prefetch_multiplier=config.get("worker.prefetch_multiplier", 1),
            worker_concurrency=config.get("worker.concurrency", 4),
            worker_max_tasks_per_child=config.get("worker.max_tasks_per_child", 1000),
            worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
            worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
            worker_hijack_root_logger=False,
            worker_log_color=False,
            task_acks_late=False,  # Acknowledge tasks immediately when received
            task_reject_on_worker_lost=False,  # Don't reject tasks on worker loss
            task_send_sent_event=True,
            task_track_started=True,
            task_ignore_result=False,
            worker_disable_rate_limits=True,
            # Prevent automatic retries at the broker level
            task_always_eager=False,
            task_store_eager_result=False,
            # Ensure tasks don't get redelivered
            task_default_retry_delay=60,
            task_max_retries=0,
            # Prevent task redelivery on worker restart/failure
            task_acks_on_failure_or_timeout=True,
        )

        return celery_app
    else:
        return None


celery_app = make_celery()

# Import GPU memory tasks to register them
if celery_app:
    try:
        logger.info("GPU memory tasks registered successfully")
    except ImportError as e:
        logger.warning(f"Failed to import GPU memory tasks: {e}")


@celery_app.task(bind=True, autoretry_for=(), max_retries=0, retry_backoff=False)
def process_video(self, job_uuid: str, task_params_dict: dict) -> dict:
    task_params = {}
    task_id = self.request.id if self.request.id else ""

    worker_hostname = socket.gethostname()
    worker_id = self.request.hostname if self.request.hostname else "unknown"

    job_tracker.update_job_state(job_uuid, "process", "started", None, self.request.id, worker_id, worker_hostname)

    temp_base_dir = str(config.get("file_processing.video_temp_directory", "/tmp"))
    video_path = os.path.join(temp_base_dir, f"{job_uuid}.video.mp4")

    try:
        task_params = TaskParameters(**task_params_dict)
        # Set logging context for this Celery task with safe attribute access
        trace_id = task_params.ot.trace_id if task_params.ot and task_params.ot.trace_id else ""
        set_logging_context(job_uuid=job_uuid, trace_id=trace_id, task_id=task_id)

        # Pre Process
        # this step is added for tasks that may need to be done first of all.
        # from app.step00_preprocess import step00_preprocess_run
        # run_step(step00_preprocess_run, job_uuid, task_params_dict, video_path)

        # File Download
        step10_download_file_run(job_uuid, task_params.resources.url, video_path)

        # File Validate
        step20_validate_file_run(job_uuid, video_path)

        # Object Detection
        step30_before_detection_run(job_uuid, task_params_dict, video_path)

        # Send Results
        # run_step(step40_send_result_run, job_uuid, task_params, "success", "")
        step40_send_result_run(job_uuid, task_params, "success", "")

        # Clean Environment
        step50_clean_environment_run(job_uuid, video_path)

        job_tracker.update_job_state(job_uuid, "process", "finished")
        # Increment successful counter when result is sent successfully
        db_manager.increment_job_counter("successful")
        return {"status": "success", "uuid": job_uuid}
    except Exception as e:
        err_msg = str(e)
        if err_msg[0:2] == "40":
            # We are not retrying because the error occurred in the step40_send_result_run method.
            pass
        else:
            try:
                step40_send_result_run(job_uuid, task_params, "failed", err_msg)
            except Exception:
                logger.warning("Failed to send result for failed job")

        job_tracker.update_job_state(job_uuid, "process", "finished-failed", "")
        return {"status": "failed", "uuid": job_uuid, "error": str(e)}
    finally:
        logger.info("finished task")
        db_manager.increment_job_counter("completed")
        # Ensure local file cleanup if something went wrong before step50
        if os.path.exists(video_path):
            try:
                os.remove(video_path)
            except Exception as cleanup_err:
                logger.warning(f"Failed to remove temp file {video_path}: {cleanup_err}")


if __name__ == "__main__":
    celery_app.start()
