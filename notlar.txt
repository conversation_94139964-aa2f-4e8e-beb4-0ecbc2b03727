calismasi icin:

postgres:
docker run -d --name postgres15 -e POSTGRES_DB=object_detection -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=4GfW42eVb -p 5432:5432 postgres:15

docker exec -it postgres15 bash -c "apt-get update && apt-get install -y postgresql-15-cron && psql -U postgres -d object_detection -c 'CREATE EXTENSION IF NOT EXISTS pg_cron;'"


postgres pg_cron extension:
docker exec -it postgres15 psql -U postgres -d object_detection -c "CREATE EXTENSION pg_cron;"

rabbitmq:
docker run -d --name rabbitmq -e RABBITMQ_DEFAULT_USER=rabbitusr -e RABBITMQ_DEFAULT_PASS=1lqgEJU3VPyhg -p 5672:5672 -p 15672:15672 rabbitmq:3-management